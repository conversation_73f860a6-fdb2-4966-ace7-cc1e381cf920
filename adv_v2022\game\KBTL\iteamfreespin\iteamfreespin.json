{"skeleton": {"hash": "DzulPG0Bp7XmhkVArhx4dUOBDu4", "spine": "3.6.53", "width": 173, "height": 158, "images": "./images/"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root"}, {"name": "bone2", "parent": "root", "x": 8.6, "y": -44.55}, {"name": "than", "parent": "bone2", "length": 68.46, "rotation": 81.63, "x": 2.49, "y": 8.47}, {"name": "<PERSON><PERSON><PERSON>", "parent": "than", "x": 84.1, "y": -15.94}, {"name": "matphai1", "parent": "<PERSON><PERSON><PERSON>", "x": 3.97, "y": 2.46}, {"name": "mattrai", "parent": "than", "x": 73.25, "y": 15.3}, {"name": "mattrai1", "parent": "mattrai", "x": 2.69, "y": 4.56}, {"name": "mom", "parent": "than", "x": 35.21, "y": -16.97}, {"name": "txt", "parent": "root", "x": 4.13, "y": -53.98}, {"name": "v<PERSON><PERSON><PERSON>", "parent": "than", "length": 12.21, "rotation": 110.13, "x": 11.67, "y": 40.47}, {"name": "vaylung", "parent": "than", "length": 19.77, "rotation": 57.45, "x": 50.79, "y": 48.24}, {"name": "v<PERSON><PERSON>i", "parent": "than", "length": 13.94, "rotation": -171.63, "x": 6.41, "y": -33.29}, {"name": "vay<PERSON>i", "parent": "than", "length": 16.31, "rotation": 176.03, "x": -1.07, "y": 20.98}], "slots": [{"name": "nen", "bone": "bone", "attachment": "nen"}, {"name": "vaylung", "bone": "vaylung", "attachment": "vaylung"}, {"name": "v<PERSON><PERSON><PERSON>", "bone": "v<PERSON><PERSON><PERSON>", "attachment": "v<PERSON><PERSON><PERSON>"}, {"name": "v<PERSON><PERSON>i", "bone": "v<PERSON><PERSON>i", "attachment": "v<PERSON><PERSON>i"}, {"name": "than", "bone": "than", "attachment": "than"}, {"name": "vay<PERSON>i", "bone": "vay<PERSON>i", "attachment": "vay<PERSON>i"}, {"name": "mom", "bone": "mom", "attachment": "mom"}, {"name": "mattrai", "bone": "mattrai", "attachment": "mattrai"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "mattrai1", "bone": "mattrai1", "attachment": "mattrai1"}, {"name": "matphai1", "bone": "matphai1", "attachment": "matphai1"}, {"name": "txt", "bone": "txt", "attachment": "txt"}, {"name": "khay", "bone": "root", "attachment": "khay"}], "skins": {"default": {"khay": {"khay": {"x": 1.34, "y": -1.81, "width": 170, "height": 156}}, "matphai": {"matphai": {"x": 0.36, "y": 0.32, "rotation": -81.63, "width": 17, "height": 16}}, "matphai1": {"matphai1": {"x": 0.62, "y": -0.01, "rotation": -81.63, "width": 8, "height": 9}}, "mattrai": {"mattrai": {"x": -1.02, "y": 0.63, "rotation": -81.63, "width": 15, "height": 15}}, "mattrai1": {"mattrai1": {"x": 0.73, "y": 0.26, "rotation": -81.63, "width": 8, "height": 9}}, "mom": {"mom": {"x": -4.58, "y": 3.03, "rotation": -81.63, "width": 50, "height": 44}}, "nen": {"nen": {"x": 1.84, "y": -1.81, "width": 173, "height": 158}}, "than": {"than": {"x": 46.77, "y": 0.06, "rotation": -81.63, "width": 115, "height": 118}}, "txt": {"txt": {"x": -1.29, "y": -1.82, "width": 127, "height": 34}}, "vayduoi": {"vayduoi": {"x": 16.54, "y": -8.4, "rotation": 168.23, "width": 53, "height": 54}}, "vaylung": {"vaylung": {"x": 9.54, "y": 0.32, "rotation": -139.09, "width": 30, "height": 32}}, "vayphai": {"vayphai": {"x": 11.21, "y": 0.38, "rotation": 90, "width": 18, "height": 34}}, "vaytrai": {"vaytrai": {"x": 10.37, "y": 0.38, "rotation": 102.34, "width": 22, "height": 35}}}}, "animations": {"animation": {"slots": {"khay": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}]}}, "bones": {"bone2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": 5.29, "curve": "stepped"}, {"time": 1.5, "angle": 5.29}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1667, "x": 0, "y": 3.89, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 3.89}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.1667, "x": 1.065, "y": 1.065, "curve": "stepped"}, {"time": 1.5, "x": 1.065, "y": 1.065}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "vaytrai": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": -28.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": -28.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -28.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -28.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -28.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -28.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": -28.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -28.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": -28.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": -28.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "vayphai": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0}, {"time": 0.2333, "angle": 27.74}, {"time": 0.3, "angle": 0}, {"time": 0.3667, "angle": 27.74}, {"time": 0.4333, "angle": 0}, {"time": 0.5, "angle": 27.74}, {"time": 0.5667, "angle": 0}, {"time": 0.6333, "angle": 27.74}, {"time": 0.7, "angle": 0}, {"time": 0.7667, "angle": 27.74}, {"time": 0.8333, "angle": 0}, {"time": 0.9, "angle": 27.74}, {"time": 0.9667, "angle": 0}, {"time": 1.0333, "angle": 27.74}, {"time": 1.1, "angle": 0}, {"time": 1.1667, "angle": 27.74}, {"time": 1.2333, "angle": 0}, {"time": 1.3, "angle": 27.74}, {"time": 1.3667, "angle": 0}, {"time": 1.4333, "angle": 27.74}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "vaylung": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 19.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 19.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 19.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 19.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 19.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 19.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 19.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 19.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": 19.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 19.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "x": 0.971, "y": 0.924, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 0.971, "y": 0.924, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0.971, "y": 0.924, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 0.971, "y": 0.924, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "x": 0.971, "y": 0.924, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 0.971, "y": 0.924, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 0.971, "y": 0.924, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 0.971, "y": 0.924, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 0.971, "y": 0.924, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 0.971, "y": 0.924, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "vayduoi": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": -10.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": -10.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -10.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -10.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -10.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -10.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": -10.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -10.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": -10.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": -10.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "x": 1, "y": 0.924, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 1, "y": 0.924, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1, "y": 0.924, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 1, "y": 0.924, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "x": 1, "y": 0.924, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 1, "y": 0.924, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 1, "y": 0.924, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 1, "y": 0.924, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 1, "y": 0.924, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 1, "y": 0.924, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "mom": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "x": 0.824, "y": 0.824, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 0.824, "y": 0.824, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0.824, "y": 0.824, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 0.824, "y": 0.824, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "x": 0.824, "y": 0.824, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 0.824, "y": 0.824, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 0.824, "y": 0.824, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 0.824, "y": 0.824, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 0.824, "y": 0.824, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 0.824, "y": 0.824, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "mattrai1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0.05, "y": -2.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0.05, "y": -2.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0.05, "y": -2.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0.05, "y": -2.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "mattrai": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": -0.09, "y": 3.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": -0.09, "y": 3.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -0.09, "y": 3.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": -0.09, "y": 3.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "x": 1.209, "y": 1.209, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 1.209, "y": 1.209, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1.209, "y": 1.209, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 1.209, "y": 1.209, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "x": 1.209, "y": 1.209, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 1.209, "y": 1.209, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 1.209, "y": 1.209, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 1.209, "y": 1.209, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 1.209, "y": 1.209, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 1.209, "y": 1.209, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "matphai1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.3333, "x": -0.03, "y": 1.24}, {"time": 0.5, "x": 0, "y": 0}, {"time": 0.6667, "x": -0.03, "y": 1.24}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1, "x": -0.03, "y": 1.24}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.3333, "x": -0.03, "y": 1.24}, {"time": 1.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "matphai": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0.11, "y": -4.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0.11, "y": -4.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0.11, "y": -4.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0.11, "y": -4.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "x": 1.209, "y": 1.209, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 1.209, "y": 1.209, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1.209, "y": 1.209, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 1.209, "y": 1.209, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "x": 1.209, "y": 1.209, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 1.209, "y": 1.209, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 1.209, "y": 1.209, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 1.209, "y": 1.209, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 1.209, "y": 1.209, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 1.209, "y": 1.209, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "than": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 3.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 3.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 4.09, "y": -0.38, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 4.09, "y": -0.38, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "txt": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 0, "y": -3.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0, "y": -3.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 0, "y": -3.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 0, "y": -3.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0, "y": -3.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 0.904, "y": 0.904, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0.904, "y": 0.904, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 0.904, "y": 0.904, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 0.904, "y": 0.904, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0.904, "y": 0.904, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}}}}}