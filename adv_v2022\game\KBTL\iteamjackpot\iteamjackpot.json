{"skeleton": {"hash": "Zpc5IHlTomYGkpG1II/ZrHwFWrI", "spine": "3.6.53", "width": 173.28, "height": 159.62, "images": "./images/"}, "bones": [{"name": "root"}, {"name": "thanchim", "parent": "root", "x": -19.2, "y": -48.15}, {"name": "thanchim3", "parent": "thanchim", "length": 68.61, "rotation": 68.84, "x": 2.75, "y": 6.19}, {"name": "<PERSON><PERSON><PERSON>", "parent": "thanchim3", "rotation": -68.84, "x": 95.31, "y": -24.68}, {"name": "cham<PERSON><PERSON><PERSON>", "parent": "<PERSON><PERSON><PERSON>", "x": -3.38, "y": 2.82}, {"name": "mattrai", "parent": "thanchim3", "rotation": -68.84, "x": 90.3, "y": -2.67}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parent": "mattrai", "x": -0.53, "y": 4.03}, {"name": "chan<PERSON>i", "parent": "thanchim3", "length": 12.95, "rotation": -148.13, "x": -4.25, "y": -20.46}, {"name": "<PERSON>rai", "parent": "thanchim3", "length": 13.97, "rotation": 149.16, "x": -2.04, "y": 29.09}, {"name": "duoi", "parent": "thanchim3", "length": 11.86, "rotation": 50.7, "x": 11.94, "y": 39.67}, {"name": "khay", "parent": "root"}, {"name": "<PERSON><PERSON><PERSON>", "parent": "thanchim3", "length": 11.01, "rotation": -17.5, "x": 108.44, "y": -8.56}, {"name": "mom", "parent": "thanchim3", "length": 14.45, "rotation": -70.2, "x": 56.73, "y": -11.24}, {"name": "luoi", "parent": "mom", "length": 10.12, "rotation": 19.18, "x": 3.4, "y": -12.65}, {"name": "mi<PERSON>i", "parent": "thanchim3", "length": 19.81, "rotation": -48.52, "x": 104.77, "y": -11.46}, {"name": "mitrai", "parent": "thanchim3", "length": 24.15, "rotation": 75.43, "x": 104.31, "y": -6.47}, {"name": "ta<PERSON>phai", "parent": "thanchim3", "length": 21.25, "rotation": -39.78, "x": 64.6, "y": -39.18}, {"name": "taytrai", "parent": "thanchim3", "length": 22.2, "rotation": 33.69, "x": 45.1, "y": 34.06}, {"name": "txt", "parent": "root", "x": 5.75, "y": -48.67}], "slots": [{"name": "nen", "bone": "khay", "attachment": "nen"}, {"name": "duoi", "bone": "duoi", "attachment": "duoi"}, {"name": "<PERSON>rai", "bone": "<PERSON>rai", "attachment": "<PERSON>rai"}, {"name": "ta<PERSON>phai", "bone": "ta<PERSON>phai", "attachment": "ta<PERSON>phai"}, {"name": "chan<PERSON>i", "bone": "chan<PERSON>i", "attachment": "chan<PERSON>i"}, {"name": "thanchim", "bone": "thanchim3", "attachment": "thanchim"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "mattrai", "bone": "mattrai", "attachment": "mattrai"}, {"name": "mom", "bone": "mom", "attachment": "mom"}, {"name": "cham<PERSON><PERSON><PERSON>", "bone": "cham<PERSON><PERSON><PERSON>", "attachment": "cham<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "taytrai", "bone": "taytrai", "attachment": "taytrai"}, {"name": "luoi", "bone": "luoi", "attachment": "luoi"}, {"name": "mi<PERSON>i", "bone": "mi<PERSON>i", "attachment": "mi<PERSON>i"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "mitrai", "bone": "mitrai", "attachment": "mitrai"}, {"name": "khay", "bone": "root", "attachment": "khay"}, {"name": "txt", "bone": "txt", "attachment": "txt"}], "skins": {"default": {"chammatphai": {"chammatphai": {"x": -0.41, "y": -1.72, "width": 7, "height": 10}}, "chammatrai": {"chammatrai": {"x": -1.43, "y": 0.8, "width": 10, "height": 10}}, "chanphai": {"chanphai": {"x": 10.12, "y": -3.36, "rotation": 79.29, "width": 24, "height": 26}}, "chantrai": {"chantrai": {"x": 16.08, "y": 2.69, "rotation": 142, "width": 34, "height": 33}}, "duoi": {"duoi": {"x": 6.03, "y": 0.25, "rotation": -119.54, "width": 14, "height": 23}}, "khay": {"khay": {"x": 1.46, "y": 3.75, "width": 173, "height": 158}}, "longdau": {"longdau": {"x": 5.89, "y": -1.69, "rotation": -51.34, "width": 20, "height": 27}}, "luoi": {"luoi": {"x": 7.99, "y": -3.14, "rotation": -17.82, "width": 27, "height": 19}}, "matphai": {"matphai": {"x": -0.8, "y": 0.1, "width": 13, "height": 14}}, "mattrai": {"mattrai": {"x": 0.04, "y": 1.83, "width": 14, "height": 16}}, "miphai": {"miphai": {"x": 11.41, "y": 1.65, "rotation": -20.32, "width": 35, "height": 22}}, "mitrai": {"mitrai": {"x": 17.51, "y": -1.12, "rotation": -144.27, "width": 36, "height": 34}}, "mom": {"mom": {"x": 10.67, "y": -0.01, "rotation": 1.36, "width": 39, "height": 63}}, "nen": {"nen": {"x": 1.18, "y": 3.12, "width": 173, "height": 158}}, "tayphai": {"tayphai": {"x": 14.6, "y": -4.13, "rotation": -29.05, "width": 35, "height": 41}}, "taytrai": {"taytrai": {"x": 17.07, "y": 4.29, "rotation": -102.53, "width": 45, "height": 56}}, "thanchim": {"thanchim": {"x": 46.85, "y": 2.45, "rotation": -68.84, "width": 109, "height": 129}}, "txt": {"txt": {"x": -1.57, "y": -3.21, "width": 155, "height": 36}}}}, "animations": {"animation": {"slots": {"khay": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "txt": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 0.827, "y": 0.827, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0.827, "y": 0.827, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 0.827, "y": 0.827, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 0.827, "y": 0.827, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0.827, "y": 0.827, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "taytrai": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 17.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 17.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 17.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 17.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 17.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 17.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 17.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 17.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": 17.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 17.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 1, "y": 0.935, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 1, "y": 0.935, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1, "y": 0.935, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1, "y": 0.935, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "tayphai": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 17.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 17.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 17.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 17.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 17.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 17.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 17.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 17.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": 17.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 17.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 1, "y": 0.935, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 1, "y": 0.935, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1, "y": 0.935, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1, "y": 0.935, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "luoi": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "x": 1.397, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 1.397, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1.397, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 1.397, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "x": 1.397, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 1.397, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 1.397, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 1.397, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 1.397, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 1.397, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "mom": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 4.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 4.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 4.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 4.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 4.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 4.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 4.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 4.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": 4.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 4.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "x": 1, "y": 0.852, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 1, "y": 0.852, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1, "y": 0.852, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 1, "y": 0.852, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "x": 1, "y": 0.852, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 1, "y": 0.852, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 1, "y": 0.852, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 1, "y": 0.852, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 1, "y": 0.852, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 1, "y": 0.852, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "thanchim3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 4.17, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 4.17, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 4.17, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 4.17, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "mitrai": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 6.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 6.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 6.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 6.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 6.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 6.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 6.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 6.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": 6.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 6.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.3, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "miphai": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": -5.51, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 6.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 6.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 6.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 6.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 6.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 6.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 6.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": 6.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 6.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "chammatrai": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "mattrai": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "x": 1.095, "y": 1.095, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 1.095, "y": 1.095, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1.095, "y": 1.095, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 1.095, "y": 1.095, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "x": 1.095, "y": 1.095, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 1.095, "y": 1.095, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 1.095, "y": 1.095, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 1.095, "y": 1.095, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 1.095, "y": 1.095, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 1.095, "y": 1.095, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "chammatphai": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "matphai": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "x": 1.095, "y": 1.095, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 1.095, "y": 1.095, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1.095, "y": 1.095, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 1.095, "y": 1.095, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "x": 1.095, "y": 1.095, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 1.095, "y": 1.095, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 1.095, "y": 1.095, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 1.095, "y": 1.095, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 1.095, "y": 1.095, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 1.095, "y": 1.095, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "longdau": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0}, {"time": 0.2333, "angle": 17.46}, {"time": 0.3, "angle": 0}, {"time": 0.3667, "angle": 17.46}, {"time": 0.4333, "angle": 0}, {"time": 0.5, "angle": 17.46}, {"time": 0.5667, "angle": 0}, {"time": 0.6333, "angle": 17.46}, {"time": 0.7, "angle": 0}, {"time": 0.7667, "angle": 17.46}, {"time": 0.8333, "angle": 0}, {"time": 0.9, "angle": 17.46}, {"time": 0.9667, "angle": 0}, {"time": 1.0333, "angle": 17.46}, {"time": 1.1, "angle": 0}, {"time": 1.1667, "angle": 17.46}, {"time": 1.2333, "angle": 0}, {"time": 1.3, "angle": 17.46}, {"time": 1.3667, "angle": 0}, {"time": 1.4333, "angle": 17.46}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 1}, {"time": 0.2333, "x": 1.148, "y": 1}, {"time": 0.3, "x": 1, "y": 1}, {"time": 0.3667, "x": 1.148, "y": 1}, {"time": 0.4333, "x": 1, "y": 1}, {"time": 0.5, "x": 1.148, "y": 1}, {"time": 0.5667, "x": 1, "y": 1}, {"time": 0.6333, "x": 1.148, "y": 1}, {"time": 0.7, "x": 1, "y": 1}, {"time": 0.7667, "x": 1.148, "y": 1}, {"time": 0.8333, "x": 1, "y": 1}, {"time": 0.9, "x": 1.148, "y": 1}, {"time": 0.9667, "x": 1, "y": 1}, {"time": 1.0333, "x": 1.148, "y": 1}, {"time": 1.1, "x": 1, "y": 1}, {"time": 1.1667, "x": 1.148, "y": 1}, {"time": 1.2333, "x": 1, "y": 1}, {"time": 1.3, "x": 1.148, "y": 1}, {"time": 1.3667, "x": 1, "y": 1}, {"time": 1.4333, "x": 1.148, "y": 1}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "duoi": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 1}, {"time": 0.2333, "x": 0.924, "y": 1}, {"time": 0.3, "x": 1, "y": 1}, {"time": 0.3667, "x": 0.924, "y": 1}, {"time": 0.4333, "x": 1, "y": 1}, {"time": 0.5, "x": 0.924, "y": 1}, {"time": 0.5667, "x": 1, "y": 1}, {"time": 0.6333, "x": 0.924, "y": 1}, {"time": 0.7, "x": 1, "y": 1}, {"time": 0.7667, "x": 0.924, "y": 1}, {"time": 0.8333, "x": 1, "y": 1}, {"time": 0.9, "x": 0.924, "y": 1}, {"time": 0.9667, "x": 1, "y": 1}, {"time": 1.0333, "x": 0.924, "y": 1}, {"time": 1.1, "x": 1, "y": 1}, {"time": 1.1667, "x": 0.924, "y": 1}, {"time": 1.2333, "x": 1, "y": 1}, {"time": 1.3, "x": 0.924, "y": 1}, {"time": 1.3667, "x": 1, "y": 1}, {"time": 1.4333, "x": 0.924, "y": 1}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "chantrai": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": -16.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": -16.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -16.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -16.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -16.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -16.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": -16.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -16.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": -16.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": -16.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "chanphai": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 27.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 27.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 27.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 27.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 27.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 27.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 27.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 27.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": 27.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 27.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "thanchim": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.1667, "x": 1.084, "y": 1.125, "curve": "stepped"}, {"time": 1.5, "x": 1.084, "y": 1.125}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "khay": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}}}}}