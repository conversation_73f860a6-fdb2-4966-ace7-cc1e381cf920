{"skins": {"default": {"jp star": {"jp star": {"x": 3.3, "width": 89, "y": 0.38, "height": 44}}, "mail 1": {"mail 1": {"x": -0.51, "width": 59, "y": 9.95, "height": 48}}, "mail 2": {"mail 2": {"x": -0.51, "width": 59, "y": 9.95, "height": 48}}, "icon menu": {"icon menu": {"x": -4.47, "width": 66, "y": -11.09, "height": 70}}, "mail 3": {"mail 3": {"x": -0.51, "width": 59, "y": 9.95, "height": 48}}, "mail": {"mail": {"x": -3.5, "width": 66, "y": -8.13, "height": 66}}, "ef/f1": {"ef/f6": {"x": 41.45, "width": 1442, "y": 69.23, "height": 180}, "ef/f7": {"x": 41.45, "width": 1442, "y": 69.23, "height": 180}, "ef/f4": {"x": 41.45, "width": 1442, "y": 69.23, "height": 180}, "ef/f5": {"x": 41.45, "width": 1442, "y": 69.23, "height": 180}, "ef/f2": {"x": 41.45, "width": 1442, "y": 69.23, "height": 180}, "ef/f3": {"x": 41.45, "width": 1442, "y": 69.23, "height": 180}, "ef/f1": {"x": 41.45, "width": 1442, "y": 69.23, "height": 180}, "ef/f16": {"x": 41.45, "width": 1442, "y": 69.23, "height": 180}, "ef/f15": {"x": 41.45, "width": 1442, "y": 69.23, "height": 180}, "ef/f12": {"x": 41.45, "width": 1442, "y": 69.23, "height": 180}, "ef/f11": {"x": 41.45, "width": 1442, "y": 69.23, "height": 180}, "ef/f14": {"x": 41.45, "width": 1442, "y": 69.23, "height": 180}, "ef/f13": {"x": 41.45, "width": 1442, "y": 69.23, "height": 180}, "ef/f10": {"x": 41.45, "width": 1442, "y": 69.23, "height": 180}, "ef/f8": {"x": 41.45, "width": 1442, "y": 69.23, "height": 180}, "ef/f9": {"x": 41.45, "width": 1442, "y": 69.23, "height": 180}}, "Chon game1": {"Chon game1": {"x": -7.97, "width": 218, "y": -1.51, "height": 94}}, "khong chon game": {"khong chon game": {"rotation": 0.04, "x": -5.3, "width": 166, "y": 0.94, "height": 94}}, "Chon game3": {"Chon game3": {"x": -7.97, "width": 218, "y": -1.51, "height": 94}}, "Chon game2": {"Chon game2": {"x": -7.97, "width": 218, "y": -1.51, "height": 94}}, "Chon game4": {"Chon game": {"x": 7.3, "width": 218, "y": 0.16, "height": 94}}, "jp star 2": {"jp star 2": {"x": 2.55, "width": 89, "y": -16.82, "height": 44}}, "jp star 3": {"jp star 3": {"x": 2.55, "width": 89, "y": -16.82, "height": 44}}, "jp star 1": {"jp star 1": {"x": 2.55, "width": 89, "y": -16.82, "height": 44}}, "icon menu 2": {"icon menu 2": {"x": 0.25, "width": 56, "y": -14.24, "height": 44}}, "nap tien kim cuong": {"nap tien kim cuong": {"x": -0.12, "width": 308, "y": 19.54, "height": 112}}, "icon menu 3": {"icon menu 3": {"x": 0.25, "width": 56, "y": -14.24, "height": 44}}, "nap tien kim cuong copy 4": {"nap tien kim cuong copy 4": {"x": -0.12, "width": 308, "y": 19.54, "height": 112}}, "effect loe sang8": {"effect loe sang": {"x": -0.35, "width": 34, "y": -0.14, "height": 34}}, "effect loe sang9": {"effect loe sang": {"x": -0.35, "width": 34, "y": -0.14, "height": 34}}, "icon menu 1": {"icon menu 1": {"x": 0.25, "width": 56, "y": -14.24, "height": 44}}, "effect loe sang6": {"effect loe sang": {"x": -0.93, "width": 34, "y": -0.38, "height": 34}}, "effect xanh": {"effect xanh": {"scaleX": 0.554, "scaleY": 0.843, "rotation": 1.56, "x": -10.25, "width": 245, "y": 1.96, "height": 53}}, "effect loe sang7": {"effect loe sang": {"x": -0.93, "width": 34, "y": -0.38, "height": 34}}, "effect loe sang4": {"effect loe sang": {"x": -0.93, "width": 34, "y": -0.38, "height": 34}}, "effect loe sang5": {"effect loe sang": {"x": -0.93, "width": 34, "y": -0.38, "height": 34}}, "nhiem vu": {"nhiem vu": {"x": -4.02, "width": 62, "y": 5.77, "height": 79}}, "thanh dai 2 copy 10": {"thanh dai 2 copy 10": {"x": 3.44, "width": 55, "y": -5.11, "height": 319}}, "effect loe sang2": {"effect loe sang": {"x": 29.1, "width": 34, "y": 83, "height": 34}}, "effect loe sang3": {"effect loe sang": {"x": -0.93, "width": 34, "y": -0.38, "height": 34}}, "JP name": {"JP name": {"x": 3.42, "width": 93, "y": -1.31, "height": 18}}, "head_patch": {"head_patch": {"lengths": [601.62, 650.29, 732.33, 1618.11], "vertices": [-930, -15.56, -746.88, -12.97, -651.8, -11.63, -157.06, -12.98, -145.26, -12.86, -122.97, -12.62, -111.07, -10.53, -100.91, 2.22, -90.65, 15.08, -71.08, 46.16, -47.95, 64.61, -31.94, 77.38], "type": "path", "vertexCount": 12}}, "coin khung": {"coin khung": {"x": -12.05, "width": 280, "y": 2.52, "height": 55}}, "ruttien": {"ruttien": {"x": -1.77, "width": 69, "y": 11.89, "height": 80}}, "effect loe sang11": {"effect loe sang": {"x": -0.35, "width": 34, "y": -0.14, "height": 34}}, "vien nap tien": {"vien nap tien": {"x": 30.73, "width": 340, "y": -56.52, "height": 132}}, "effect loe sang10": {"effect loe sang": {"x": -0.35, "width": 34, "y": -0.14, "height": 34}}, "Chon game": {"Chon game": {"x": 7.3, "width": 218, "y": 0.16, "height": 94}}, "text tien2": {"text tien": {"x": -6.16, "width": 117, "y": 2.39, "height": 53}}, "Jp khung": {"Jp khung": {"x": -0.45, "width": 108, "y": 11.12, "height": 62}}, "nhiem vu 3": {"nhiem vu 3": {"x": 1.47, "width": 51, "y": -0.54, "height": 53}}, "text nap": {"text nap": {"x": 1.45, "width": 107, "y": -6.3, "height": 55}}, "icon dang nhap": {"icon dang nhap": {"x": 4.39, "width": 61, "y": -14.91, "height": 76}}, "nhiem vu 2": {"nhiem vu 2": {"x": 1.47, "width": 51, "y": -0.54, "height": 53}}, "nhiem vu 1": {"nhiem vu 1": {"x": 1.47, "width": 51, "y": -0.54, "height": 53}}, "coin": {"coin": {"x": 1.69, "width": 18, "y": 0.55, "height": 28}}, "Khongchon game effect": {"Khongchon game effect": {"x": -13.74, "width": 163, "y": 1.26, "height": 47}}, "text tien": {"text tien": {"x": -6.16, "width": 117, "y": 2.39, "height": 53}}, "head_patch2": {"head_patch2": {"lengths": [634.22, 689.46, 766.58, 1693.91], "vertices": [982.42, -13.58, 790.68, -13.76, 600.74, -13.93, 301.31, -15.04, 156.46, -15.07, 139.11, -15.07, 126.29, -3.16, 115.23, 17.52, 107.48, 32.01, 75.04, 66.18, 58.7, 68.15, 42, 70.16], "type": "path", "vertexCount": 12}}, "head effect3": {"head effect": {"scaleX": 0.924, "scaleY": 0.265, "rotation": 0.58, "x": -391.71, "width": 245, "y": 434.11, "height": 53}}, "head effect2": {"head effect": {"x": -12.58, "width": 245, "y": 3.14, "height": 53}}, "head": {"head": {"x": 14.13, "width": 1572, "y": 0.96, "height": 106}}, "Chon game_patch": {"Chon game_patch": {"lengths": [19.06, 133.81, 154.64, 175.89, 233.59, 291.44, 314.76], "vertices": [-69.58, 2.05, -68.83, 3.66, -66.63, 8.36, -58.3, 17.97, -57.36, 18.83, -55.84, 20.21, 37.25, 18.56, 57.38, 18.48, 58.52, 18.48, 63.39, 10, 66.06, -0.23, 66.75, -2.85, 58.18, -19.1, 56.79, -19.13, 45.74, -19.33, 22.12, -18.98, -0.9, -18.95, -30.17, -18.92, -58.46, -19.14, -58.75, -18.6, -67.96, -1.06], "closed": true, "type": "path", "vertexCount": 21}}, "icon dang ky": {"icon dang ky": {"x": -4.44, "width": 72, "y": -8.63, "height": 82}}, "effect png/d1": {"effect png/d18": {"x": 0.64, "width": 421, "y": 120.06, "height": 230}, "effect png/d17": {"x": 0.64, "width": 421, "y": 120.06, "height": 230}, "effect png/d16": {"x": 0.64, "width": 421, "y": 120.06, "height": 230}, "effect png/d15": {"x": 0.64, "width": 421, "y": 120.06, "height": 230}, "effect png/d14": {"x": 0.64, "width": 421, "y": 120.06, "height": 230}, "effect png/d13": {"x": 0.64, "width": 421, "y": 120.06, "height": 230}, "effect png/d12": {"x": 0.64, "width": 421, "y": 120.06, "height": 230}, "effect png/d4": {"x": 0.64, "width": 421, "y": 120.06, "height": 230}, "effect png/d3": {"x": 0.64, "width": 421, "y": 120.06, "height": 230}, "effect png/d6": {"x": 0.64, "width": 421, "y": 120.06, "height": 230}, "effect png/d5": {"x": 0.64, "width": 421, "y": 120.06, "height": 230}, "effect png/d2": {"x": 0.64, "width": 421, "y": 120.06, "height": 230}, "effect png/d1": {"x": 0.64, "width": 421, "y": 120.06, "height": 230}, "effect png/d11": {"x": 0.64, "width": 421, "y": 120.06, "height": 230}, "effect png/d10": {"x": 0.64, "width": 421, "y": 120.06, "height": 230}, "effect png/d8": {"x": 0.64, "width": 421, "y": 120.06, "height": 230}, "effect png/d7": {"x": 0.64, "width": 421, "y": 120.06, "height": 230}, "effect png/d9": {"x": 0.64, "width": 421, "y": 120.06, "height": 230}}, "nap tien effect": {"nap tien effect": {"x": -13.28, "width": 245, "y": -5.19, "height": 53}}, "effect loe sang - Copy": {"effect loe sang - Copy": {"x": 1.32, "width": 34, "y": -0.62, "height": 34}}, "ruttien 3": {"ruttien 3": {"x": 1.14, "width": 62, "y": -8.46, "height": 58}}, "ruttien 2": {"ruttien 2": {"x": 1.14, "width": 62, "y": -8.46, "height": 58}}, "ruttien 1": {"ruttien 1": {"x": 1.14, "width": 62, "y": -8.46, "height": 58}}, "head effect": {"head effect": {"x": -12.58, "width": 245, "y": 2.03, "height": 53}}, "khong chon game2": {"khong chon game": {"rotation": 0.04, "x": -5.3, "width": 166, "y": 0.94, "height": 94}}, "text nap2": {"text nap": {"x": 1.45, "width": 107, "y": -6.3, "height": 55}}, "dang nhap": {"dang nhap": {"x": -0.35, "width": 201, "y": -9.53, "height": 57}}, "dang ky": {"dang ky": {"x": 0.57, "width": 125, "y": -4.45, "height": 43}}, "avatar": {"avatar": {"x": -611.4, "width": 105, "y": 203, "height": 112}}, "menu": {"menu": {"x": 1.71, "width": 78, "y": -5.09, "height": 37}}, "effect loe sang": {"effect loe sang": {"x": -0.93, "width": 34, "y": -0.38, "height": 34}}, "thanh duoi": {"thanh duoi": {"x": 15.53, "width": 1572, "y": 58.71, "height": 124}}, "vien 1": {"vien 1": {"x": 2.84, "width": 325, "y": -53.3, "height": 118}}}}, "skeleton": {"images": "./images/", "width": 0, "spine": "3.7.94", "audio": "F:/1.Atek/2.GO88/Finish/animation/Spine/File soan - Copy", "hash": "mK8zvLQT10dXxXSiH1j60TAwR7Q", "height": 0}, "path": [{"bones": ["effect loe sang - <PERSON><PERSON>"], "name": "Chon game_patch", "order": 0, "target": "Chon game_patch"}, {"bones": ["head effect"], "name": "head_patch", "order": 1, "target": "head_patch"}, {"bones": ["head effect2"], "name": "head_patch2", "order": 2, "target": "head_patch2"}], "slots": [{"name": "Chon game", "bone": "Chon game"}, {"color": "ffffff89", "blend": "additive", "name": "Chon game4", "bone": "Chon game"}, {"name": "Chon game1", "bone": "Chon game1"}, {"name": "Chon game2", "bone": "Chon game1"}, {"name": "Chon game3", "bone": "Chon game1"}, {"name": "khong chon game", "bone": "khong chon game"}, {"blend": "additive", "name": "khong chon game2", "bone": "khong chon game"}, {"name": "Khongchon game effect", "bone": "Khongchon game effect"}, {"name": "head", "bone": "head"}, {"name": "head effect", "bone": "head effect"}, {"name": "head effect3", "bone": "root"}, {"name": "head effect2", "bone": "head effect2"}, {"name": "thanh duoi", "bone": "thanh duoi"}, {"name": "nap tien kim cuong", "bone": "nap tien kim cuong"}, {"name": "nap tien kim cuong copy 4", "bone": "nap tien kim cuong"}, {"name": "vien 1", "bone": "vien 1"}, {"name": "vien nap tien", "bone": "vien nap tien"}, {"name": "effect loe sang", "bone": "effect loe sang"}, {"name": "effect loe sang3", "bone": "effect loe sang2"}, {"name": "effect loe sang4", "bone": "effect loe sang3"}, {"name": "effect loe sang7", "bone": "effect loe sang6"}, {"name": "effect loe sang6", "bone": "effect loe sang5"}, {"name": "effect loe sang5", "bone": "effect loe sang4"}, {"name": "effect loe sang2", "bone": "root"}, {"name": "text tien", "bone": "text tien"}, {"blend": "additive", "name": "text tien2", "bone": "text tien"}, {"name": "text nap", "bone": "text nap"}, {"blend": "additive", "name": "text nap2", "bone": "text nap"}, {"name": "mail", "bone": "mail"}, {"name": "mail 1", "bone": "mail2"}, {"name": "mail 2", "bone": "mail2"}, {"name": "mail 3", "bone": "mail2"}, {"name": "nhiem vu", "bone": "nhiem vu"}, {"name": "nhiem vu 1", "bone": "nhiem vu2"}, {"name": "nhiem vu 2", "bone": "nhiem vu2"}, {"name": "nhiem vu 3", "bone": "nhiem vu2"}, {"name": "menu", "bone": "menu"}, {"name": "icon menu", "bone": "icon menu"}, {"name": "icon menu 1", "bone": "icon menu2"}, {"name": "icon menu 2", "bone": "icon menu2"}, {"name": "icon menu 3", "bone": "icon menu2"}, {"name": "ruttien", "bone": "ruttien"}, {"name": "ruttien 1", "bone": "ruttien2"}, {"name": "ruttien 2", "bone": "ruttien2"}, {"name": "ruttien 3", "bone": "ruttien2"}, {"name": "<PERSON>p khung", "bone": "<PERSON>p khung"}, {"name": "JP name", "bone": "JP name"}, {"name": "jp star", "bone": "jp star"}, {"name": "jp star 1", "bone": "jp star2"}, {"name": "jp star 2", "bone": "jp star2"}, {"name": "jp star 3", "bone": "jp star2"}, {"name": "effect loe sang8", "bone": "effect sang"}, {"name": "effect loe sang9", "bone": "effect sang2"}, {"name": "effect loe sang10", "bone": "effect sang3"}, {"name": "effect loe sang11", "bone": "effect sang4"}, {"name": "thanh dai 2 copy 10", "bone": "thanh dai 2 copy 10"}, {"name": "avatar", "bone": "root"}, {"name": "coin khung", "bone": "coin khung"}, {"name": "coin", "bone": "coin"}, {"name": "icon dang ky", "bone": "icon dang ky"}, {"name": "dang ky", "bone": "dang ky"}, {"name": "dang nhap", "bone": "dang nhap"}, {"name": "icon dang nhap", "bone": "icon dang nhap"}, {"color": "ffffff20", "name": "ef/f1", "bone": "effect thanh duoi"}, {"name": "Chon game_patch", "bone": "Chon game"}, {"name": "effect loe sang - <PERSON><PERSON>", "bone": "effect loe sang - <PERSON><PERSON>"}, {"name": "effect xanh", "bone": "effect xanh"}, {"attachment": "head_patch", "name": "head_patch", "bone": "head"}, {"name": "head_patch3", "bone": "head"}, {"attachment": "head_patch2", "name": "head_patch2", "bone": "head"}, {"name": "head_patch4", "bone": "head"}, {"name": "effect png/d1", "bone": "effect nap tien kim cuong"}, {"name": "nap tien effect", "bone": "nap tien effect"}], "bones": [{"rotation": 0.03, "name": "root"}, {"parent": "root", "color": "00ff69ff", "name": "Screen"}, {"parent": "Screen", "color": "00ff69ff", "name": "nap tien kim cuong", "x": 39.23, "y": 136.46}, {"parent": "nap tien kim cuong", "color": "00ff69ff", "name": "vien 1", "x": -3.47, "y": 74.84}, {"parent": "nap tien kim cuong", "color": "00ff69ff", "name": "vien nap tien", "x": -32.86, "y": 79.07}, {"parent": "nap tien kim cuong", "color": "00ff69ff", "name": "nap tien effect", "x": -3.35, "y": 85.24}, {"parent": "nap tien kim cuong", "color": "00ff69ff", "name": "text nap", "x": -61.07, "y": 17.34}, {"parent": "nap tien kim cuong", "color": "00ff69ff", "name": "text tien", "x": 65.53, "y": 18.65}, {"parent": "Screen", "color": "ff0500ff", "name": "Chon game", "x": -515.19, "y": 713.84}, {"parent": "Chon game", "color": "ff0500ff", "name": "Chon game1", "x": 15.27, "y": 1.67}, {"parent": "Screen", "color": "ffa700ff", "name": "khong chon game", "x": -294.6, "y": 713.06}, {"parent": "khong chon game", "color": "ff6e00ff", "name": "Khongchon game effect", "x": 16.94, "y": -0.82}, {"parent": "Screen", "color": "00ff69ff", "name": "<PERSON>p khung", "x": 281.56, "y": 117.88}, {"parent": "<PERSON>p khung", "color": "00ff69ff", "name": "JP name", "x": -0.37, "y": 25.43}, {"parent": "<PERSON>p khung", "color": "00ff69ff", "name": "jp star", "x": 0.75, "y": 52.73}, {"parent": "jp star", "color": "00ff69ff", "name": "jp star2", "x": 0.75, "y": 17.2}, {"parent": "Screen", "color": "a900ffff", "name": "thanh duoi", "x": 3.57, "y": 98.29}, {"parent": "thanh duoi", "color": "a900ffff", "name": "effect thanh duoi", "x": -0.36, "y": -33.54}, {"parent": "Screen", "color": "ff7f00ff", "name": "coin khung", "x": -377.85, "y": 138.98}, {"parent": "coin khung", "color": "ff9300ff", "name": "coin", "x": -111.74, "y": -1.53}, {"parent": "Screen", "color": "00ff69ff", "name": "mail", "x": 529.61, "y": 155.13}, {"parent": "mail", "color": "00ff69ff", "name": "mail2", "x": 0.51, "y": -9.08}, {"parent": "Screen", "color": "00ff69ff", "name": "menu", "x": 696.4, "y": 117.59}, {"parent": "menu", "color": "00ff69ff", "name": "icon menu", "x": -5.83, "y": 65.51}, {"parent": "icon menu", "color": "00ff69ff", "name": "icon menu2", "x": 0.28, "y": 16.14}, {"parent": "Screen", "color": "00ff69ff", "name": "nhiem vu", "x": 413.12, "y": 139.73}, {"parent": "nhiem vu", "color": "00ff69ff", "name": "nhiem vu2", "x": 0.01, "y": 19.31}, {"parent": "Screen", "color": "00ff69ff", "name": "ruttien", "x": -188.62, "y": 128.11}, {"parent": "ruttien", "color": "00ff69ff", "name": "ruttien2", "x": 0.59, "y": 31.35}, {"parent": "Screen", "color": "00ff69ff", "name": "dang ky", "x": -629.96, "y": 115.95}, {"parent": "dang ky", "color": "00ff69ff", "name": "icon dang ky", "x": 15.51, "y": 67.68}, {"parent": "Screen", "color": "00ff69ff", "name": "dang nhap", "x": -282.04, "y": 141.03}, {"parent": "dang nhap", "color": "00ff69ff", "name": "icon dang nhap", "x": -135.75, "y": -1.12}, {"parent": "nap tien kim cuong", "color": "272727ff", "name": "effect loe sang", "x": -9.2, "y": -53.07}, {"parent": "nap tien kim cuong", "color": "272727ff", "name": "effect loe sang2", "x": 26.3, "y": 26.45}, {"parent": "nap tien kim cuong", "color": "272727ff", "name": "effect loe sang3", "x": 26.3, "y": 26.45}, {"parent": "nap tien kim cuong", "color": "272727ff", "name": "effect loe sang4", "x": 26.3, "y": 26.45}, {"parent": "nap tien kim cuong", "color": "272727ff", "name": "effect loe sang5", "x": 26.3, "y": 26.45}, {"parent": "nap tien kim cuong", "color": "272727ff", "name": "effect loe sang6", "x": 26.3, "y": 26.45}, {"parent": "<PERSON>p khung", "color": "00ff69ff", "name": "effect sang", "x": -0.65, "y": -34.75}, {"parent": "<PERSON>p khung", "color": "00ff69ff", "name": "effect sang2", "x": -0.65, "y": -34.75}, {"parent": "<PERSON>p khung", "color": "00ff69ff", "name": "effect sang3", "x": -0.65, "y": -34.75}, {"parent": "<PERSON>p khung", "color": "00ff69ff", "name": "effect sang4", "x": -0.65, "y": -34.75}, {"parent": "Screen", "color": "00ff69ff", "name": "thanh dai 2 copy 10", "x": -381.84, "y": 450.61}, {"parent": "Screen", "color": "00ff69ff", "name": "head", "x": 25.79, "y": 789.51}, {"scaleX": 0.368, "parent": "Screen", "color": "00ff69ff", "name": "head effect", "x": -144.82, "y": 895.47}, {"parent": "Screen", "color": "00ff69ff", "name": "effect loe sang - <PERSON><PERSON>", "x": -495.75, "y": 784.53}, {"parent": "Chon game", "color": "ff0500ff", "name": "effect xanh", "x": 4.27, "y": 18.49}, {"scaleX": 0.368, "parent": "Screen", "color": "00ff69ff", "name": "head effect2", "x": -144.82, "y": 895.47}, {"parent": "nap tien kim cuong", "color": "00ff69ff", "name": "effect nap tien kim cuong", "x": 3.89, "y": -99.31}], "animations": {"mail": {"slots": {"mail 1": {"attachment": [{"name": "mail 1", "time": 0.1333}, {"name": null, "time": 0.2667}]}, "mail 2": {"attachment": [{"name": "mail 2", "time": 0.2667}, {"name": null, "time": 0.4}]}, "mail 3": {"attachment": [{"name": "mail 3", "time": 0.4}, {"name": null, "time": 0.5333}]}, "mail": {"attachment": [{"name": "mail", "time": 0}, {"name": "mail", "time": 1}, {"name": "mail", "time": 2}]}}, "bones": {"mail": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}, "mail2": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}}}, "thanh dai": {"slots": {"thanh dai 2 copy 10": {"color": [{"color": "ffffff59", "time": 0}, {"color": "ffffffff", "time": 0.2538}, {"color": "ffffff72", "time": 0.5}, {"color": "ffffffff", "time": 1}, {"color": "ffffff66", "time": 1.5}, {"color": "ffffff59", "time": 2}], "attachment": [{"name": "thanh dai 2 copy 10", "time": 0}]}}, "bones": {"thanh dai 2 copy 10": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}}}, "Jp": {"slots": {"jp star": {"attachment": [{"name": "jp star", "time": 0}, {"name": "jp star", "time": 1}, {"name": "jp star", "time": 2}]}, "jp star 2": {"attachment": [{"name": "jp star 2", "time": 0.2667}, {"name": null, "time": 0.4667}]}, "jp star 3": {"attachment": [{"name": "jp star 3", "time": 0.3667}, {"name": null, "time": 0.4667}]}, "JP name": {"attachment": [{"name": "JP name", "time": 0}, {"name": "JP name", "time": 1}, {"name": "JP name", "time": 2}]}, "jp star 1": {"attachment": [{"name": "jp star 1", "time": 0.1667}, {"name": null, "time": 0.2667}]}, "Jp khung": {"attachment": [{"name": "<PERSON>p khung", "time": 0}, {"name": "<PERSON>p khung", "time": 1}, {"name": "<PERSON>p khung", "time": 2}]}, "effect loe sang8": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0.1667}, {"color": "ffffffff", "time": 0.7667}, {"color": "ffffff00", "time": 0.8333}], "attachment": [{"name": "effect loe sang", "time": 0.1667}]}, "effect loe sang11": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 1.3333}, {"color": "ffffffff", "time": 1.6}, {"color": "ffffff00", "time": 1.6667}], "attachment": [{"name": "effect loe sang", "time": 1.3333}]}, "effect loe sang9": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0.8667}, {"color": "ffffffff", "time": 1.2667}, {"color": "ffffff00", "time": 1.3333}], "attachment": [{"name": "effect loe sang", "time": 0.8667}]}, "effect loe sang10": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 1.3333}, {"color": "ffffffff", "time": 1.6}, {"color": "ffffff00", "time": 1.6667}], "attachment": [{"name": "effect loe sang", "time": 1.3333}]}}, "bones": {"jp star": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1.009, "y": 1.009, "time": 0}, {"curve": "stepped", "x": 1.009, "y": 1.009, "time": 1}, {"x": 1.009, "y": 1.009, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}, "effect sang2": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"x": 0.749, "y": 0.749, "time": 0}, {"x": 0.879, "y": 0.875, "time": 0.8667}, {"x": 0.749, "y": 0.749, "time": 1.3333}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": 0.5, "y": 52.4, "time": 0}, {"x": 4.13, "y": 66.29, "time": 0.8667}, {"curve": "stepped", "x": 5.53, "y": 100.09, "time": 1.3333}, {"x": 5.53, "y": 100.09, "time": 2}]}, "effect sang3": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"x": 0.749, "y": 0.749, "time": 0}, {"x": 0.879, "y": 0.875, "time": 1.3333}, {"x": 0.749, "y": 0.749, "time": 1.6667}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": 34.47, "y": 52.4, "time": 0}, {"x": 36.43, "y": 84.75, "time": 1.3333}, {"curve": "stepped", "x": 28.6, "y": 112.25, "time": 1.6667}, {"x": 28.6, "y": 112.25, "time": 2}]}, "JP name": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"x": 1.009, "y": 1.009, "time": 0}, {"x": 1.206, "y": 1.206, "time": 0.1667}, {"curve": "stepped", "x": 1.009, "y": 1.009, "time": 0.5}, {"curve": "stepped", "x": 1.009, "y": 1.009, "time": 1}, {"x": 1.009, "y": 1.009, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}, "effect sang4": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"x": 0.749, "y": 0.749, "time": 0}, {"x": 0.879, "y": 0.875, "time": 1.3333}, {"x": 0.749, "y": 0.749, "time": 1.6667}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": 51.24, "y": 52.4, "time": 0}, {"x": 50.68, "y": 62.53, "time": 1.3333}, {"curve": "stepped", "x": 51.66, "y": 93.38, "time": 1.6667}, {"x": 51.66, "y": 93.38, "time": 2}]}, "Jp khung": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1.009, "y": 1.009, "time": 0}, {"curve": "stepped", "x": 1.009, "y": 1.009, "time": 1}, {"x": 1.009, "y": 1.009, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}, "jp star2": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1.009, "y": 1.009, "time": 0}, {"curve": "stepped", "x": 1.009, "y": 1.009, "time": 1}, {"x": 1.009, "y": 1.009, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}, "effect sang": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"x": 0.749, "y": 0.749, "time": 0}, {"x": 0.879, "y": 0.875, "time": 0.1667}, {"x": 0.749, "y": 0.749, "time": 0.8333}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": -39.76, "y": 52.4, "time": 0}, {"x": -39.76, "y": 109.32, "time": 0.8333}, {"x": -39.76, "y": 52.4, "time": 2}]}}}, "Khung duoi": {"slots": {"ef/f1": {"color": [{"color": "e6ecff1a", "time": 0}], "attachment": [{"name": "ef/f1", "time": 0}, {"name": "ef/f2", "time": 0.1333}, {"name": "ef/f3", "time": 0.2667}, {"name": "ef/f4", "time": 0.4}, {"name": "ef/f5", "time": 0.5333}, {"name": "ef/f6", "time": 0.6667}, {"name": "ef/f7", "time": 0.8}, {"name": "ef/f8", "time": 0.9333}, {"name": "ef/f9", "time": 1.0667}, {"name": "ef/f10", "time": 1.2}, {"name": "ef/f11", "time": 1.3333}, {"name": "ef/f12", "time": 1.4667}, {"name": "ef/f13", "time": 1.6}, {"name": "ef/f14", "time": 1.7333}, {"name": "ef/f15", "time": 1.8667}, {"name": "ef/f16", "time": 2}]}, "thanh duoi": {"color": [{"color": "f0ffedff", "curve": "stepped", "time": 0}, {"color": "f0ffedff", "time": 2}], "attachment": [{"name": "thanh duoi", "time": 0}, {"name": "thanh duoi", "time": 2}]}}, "bones": {"thanh duoi": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "effect thanh duoi": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"x": 1.263, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}}}, "dang nhap": {"slots": {"dang nhap": {"attachment": [{"name": "dang nhap", "time": 0}, {"name": "dang nhap", "time": 1}, {"name": "dang nhap", "time": 2}]}, "icon dang nhap": {"attachment": [{"name": "icon dang nhap", "time": 0}, {"name": "icon dang nhap", "time": 1}, {"name": "icon dang nhap", "time": 2}]}}, "bones": {"dang nhap": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}, "icon dang nhap": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"x": 1, "y": 1, "time": 0}, {"x": 1.104, "y": 1.104, "time": 0.5}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}}}, "rut tien": {"slots": {"ruttien": {"attachment": [{"name": "ruttien", "time": 0}]}, "ruttien 3": {"attachment": [{"name": "ruttien 3", "time": 0.3}, {"name": null, "time": 0.4}]}, "ruttien 2": {"attachment": [{"name": "ruttien 2", "time": 0.2}, {"name": null, "time": 0.3}]}, "ruttien 1": {"attachment": [{"name": "ruttien 1", "time": 0.1}, {"name": null, "time": 0.2}]}}, "bones": {"ruttien2": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}, "ruttien": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}}}, "coin khung": {"slots": {"coin khung": {"attachment": [{"name": "coin khung", "time": 0}]}, "coin": {"attachment": [{"name": "coin", "time": 0}]}}, "bones": {"coin khung": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}, "coin": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"x": 1, "y": 1, "time": 0}, {"x": 1.351, "y": 1.351, "time": 0.5}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}}}, "khong chon game": {"slots": {"Khongchon game effect": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}, {"color": "ffffff00", "time": 1}], "attachment": [{"name": "Khongchon game effect", "time": 0}, {"name": "Khongchon game effect", "time": 0.5}]}, "khong chon game2": {"color": [{"color": "ffffff2e", "time": 0}, {"color": "ffffffff", "time": 1}, {"color": "ffffff2e", "time": 2}], "attachment": [{"name": "khong chon game", "time": 0}, {"name": "khong chon game", "time": 1}, {"name": null, "time": 2}]}, "khong chon game": {"attachment": [{"name": "khong chon game", "time": 0}, {"name": "khong chon game", "time": 1}, {"name": "khong chon game", "time": 2}]}}, "bones": {"Khongchon game effect": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": -7.85, "y": -2.06, "time": 0}, {"curve": "stepped", "x": -7.85, "y": -2.06, "time": 1}, {"x": -7.85, "y": -2.06, "time": 2}]}, "khong chon game": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}}}, "dangky": {"slots": {"icon dang ky": {"attachment": [{"name": "icon dang ky", "time": 0}]}, "dang ky": {"attachment": [{"name": "dang ky", "time": 0}]}}, "bones": {"icon dang ky": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"x": 1, "y": 1, "time": 0}, {"x": 1.102, "y": 1.102, "time": 0.5}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}, "dang ky": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}}}, "menu": {"slots": {"icon menu": {"attachment": [{"name": "icon menu", "time": 0}, {"name": "icon menu", "time": 1}, {"name": "icon menu", "time": 2}]}, "icon menu 2": {"attachment": [{"name": "icon menu 2", "time": 0.2333}, {"name": null, "time": 0.3333}]}, "icon menu 3": {"attachment": [{"name": "icon menu 3", "time": 0.3333}, {"name": null, "time": 0.4333}]}, "icon menu 1": {"attachment": [{"name": "icon menu 1", "time": 0.1333}, {"name": null, "time": 0.2333}]}, "menu": {"attachment": [{"name": "menu", "time": 0}, {"name": "menu", "time": 1}, {"name": "menu", "time": 2}]}}, "bones": {"icon menu": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}, "menu": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}, "icon menu2": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}}}, "nap tien button4": {"slots": {"effect loe sang3": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0.5667}, {"color": "ffffff00", "time": 0.6667}], "attachment": [{"name": "effect loe sang", "time": 0.1}]}, "text tien": {"attachment": [{"name": "text tien", "time": 0}, {"name": "text tien", "time": 2}]}, "text nap2": {"attachment": [{"name": null, "time": 0}, {"name": null, "time": 2}]}, "vien nap tien": {"attachment": [{"name": null, "time": 0}, {"name": null, "time": 2}]}, "effect loe sang": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0.5667}, {"color": "ffffff00", "time": 0.6667}], "attachment": [{"name": "effect loe sang", "time": 0.1}]}, "nap tien kim cuong": {"color": [{"color": "ffffffff", "time": 2}], "attachment": [{"name": "nap tien kim cuong", "time": 0}, {"name": "nap tien kim cuong", "time": 1}, {"name": "nap tien kim cuong", "time": 3}, {"name": "nap tien kim cuong", "time": 3.3333}]}, "nap tien kim cuong copy 4": {"color": [{"color": "ffffff00", "time": 2}, {"color": "ffffffff", "time": 2.3333}, {"color": "ffffff00", "time": 2.6667}], "attachment": [{"name": "nap tien kim cuong copy 4", "time": 2}]}, "effect png/d1": {"color": [{"color": "ffffffff", "time": 1.1667}, {"color": "ffffff69", "time": 2.7333}, {"color": "ffffff00", "time": 3.1667}], "attachment": [{"name": "effect png/d1", "time": 1.1667}, {"name": "effect png/d2", "time": 1.2667}, {"name": "effect png/d3", "time": 1.3667}, {"name": "effect png/d4", "time": 1.4667}, {"name": "effect png/d5", "time": 1.5667}, {"name": "effect png/d6", "time": 1.6667}, {"name": "effect png/d7", "time": 1.7667}, {"name": "effect png/d8", "time": 1.8667}, {"name": "effect png/d9", "time": 1.9667}, {"name": "effect png/d10", "time": 2.0667}, {"name": "effect png/d11", "time": 2.1667}, {"name": "effect png/d12", "time": 2.2667}, {"name": "effect png/d13", "time": 2.3667}, {"name": "effect png/d14", "time": 2.4667}, {"name": "effect png/d15", "time": 2.5667}, {"name": "effect png/d16", "time": 2.6667}, {"name": "effect png/d17", "time": 2.7667}, {"name": "effect png/d18", "time": 2.8667}, {"name": null, "time": 2.9667}, {"name": "effect png/d1", "time": 3.0667}, {"name": "effect png/d2", "time": 3.1667}]}, "nap tien effect": {"color": [{"color": "ffffff5b", "time": 0}, {"color": "ffffffff", "time": 0.5}, {"color": "ffffff5b", "time": 1}, {"color": "ffffffff", "time": 1.5}, {"color": "ffffff5b", "time": 2}], "attachment": [{"name": "nap tien effect", "time": 0}, {"name": "nap tien effect", "time": 1}, {"name": "nap tien effect", "time": 2}]}, "effect loe sang6": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0.8333}, {"color": "ffffff00", "time": 0.9333}], "attachment": [{"name": "effect loe sang", "time": 0.3333}]}, "text nap": {"attachment": [{"name": "text nap", "time": 0}, {"name": "text nap", "time": 2}]}, "effect loe sang7": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0.5667}, {"color": "ffffff00", "time": 0.6667}], "attachment": [{"name": "effect loe sang", "time": 0.1}]}, "effect loe sang4": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0.5667}, {"color": "ffffff00", "time": 0.6667}], "attachment": [{"name": "effect loe sang", "time": 0.1}]}, "vien 1": {"attachment": [{"name": "vien 1", "time": 0}, {"name": "vien 1", "time": 1}, {"name": "vien 1", "time": 1.5}, {"name": "vien 1", "time": 2}]}, "effect loe sang5": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0.5667}, {"color": "ffffff00", "time": 0.6667}], "attachment": [{"name": "effect loe sang", "time": 0.1}]}}, "bones": {"Khongchon game effect": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "jp star": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "text tien": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "icon menu": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "mail": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "Screen": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "Chon game1": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "khong chon game": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "nap tien kim cuong": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"curve": "stepped", "angle": 0, "time": 3}, {"angle": 0, "time": 3.3333}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 1}, {"x": 1.082, "y": 1.082, "time": 1.5}, {"curve": "stepped", "x": 1, "y": 1, "time": 2}, {"curve": "stepped", "x": 1, "y": 1, "time": 3}, {"x": 1, "y": 1, "time": 3.3333}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"curve": "stepped", "x": 0, "y": 0, "time": 3}, {"x": 0, "y": 0, "time": 3.3333}]}, "icon dang ky": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "nap tien effect": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "jp star2": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "effect loe sang6": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": 39.06, "y": -48.13, "time": 0}, {"x": -113.5, "y": -46.11, "time": 0.1}, {"curve": "stepped", "x": 40.31, "y": 38.98, "time": 0.6667}, {"x": 40.31, "y": 38.98, "time": 2}]}, "effect loe sang4": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": 39.06, "y": -48.13, "time": 0}, {"x": -23.35, "y": -36.23, "time": 0.1}, {"curve": "stepped", "x": -29.37, "y": 38.98, "time": 0.6667}, {"x": -29.37, "y": 38.98, "time": 2}]}, "nhiem vu": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "effect loe sang5": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": 39.06, "y": -48.13, "time": 0}, {"x": -46.14, "y": -48.13, "time": 0.3333}, {"curve": "stepped", "x": -52.33, "y": 38.98, "time": 0.9333}, {"x": -52.33, "y": 38.98, "time": 2}]}, "effect loe sang2": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": -118.03, "y": -48.13, "time": 0}, {"x": -77.52, "y": -48.13, "time": 0.1}, {"curve": "stepped", "x": -82.83, "y": 38.98, "time": 0.6667}, {"x": -82.83, "y": 38.98, "time": 2}]}, "effect loe sang3": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 39.06, "y": -48.13, "time": 0}, {"x": 39.06, "y": -48.13, "time": 0.1}, {"curve": "stepped", "x": 40.31, "y": 38.98, "time": 0.6667}, {"x": 40.31, "y": 38.98, "time": 2}]}, "JP name": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "ruttien2": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "dang nhap": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "coin khung": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "ruttien": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "dang ky": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "vien nap tien": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "menu": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "nhiem vu2": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "Chon game": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "effect loe sang": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": -118.03, "y": 28.7, "time": 0}, {"x": -118.03, "y": 28.7, "time": 0.1}, {"curve": "stepped", "x": -82.83, "y": 115.72, "time": 0.6667}, {"x": -82.83, "y": 115.72, "time": 2}]}, "mail2": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "Jp khung": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "thanh duoi": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "text nap": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "icon menu2": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "effect thanh duoi": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "icon dang nhap": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "vien 1": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "coin": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}}}, "khong chon game idle": {"slots": {"Khongchon game effect": {"color": [{"color": "ffffff00", "time": 0}], "attachment": [{"name": "Khongchon game effect", "time": 0}]}, "khong chon game2": {"color": [{"color": "ffffff2e", "curve": "stepped", "time": 0}, {"color": "ffffff2e", "time": 2}], "attachment": [{"name": "khong chon game", "time": 0}, {"name": null, "time": 2}]}, "khong chon game": {"attachment": [{"name": "khong chon game", "time": 0}, {"name": "khong chon game", "time": 2}]}}, "bones": {"Khongchon game effect": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "khong chon game": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}}}, "head3": {"slots": {"head": {"attachment": [{"name": "head", "time": 0}]}, "head effect": {"attachment": [{"name": "head effect", "time": 0}]}, "head_patch": {"attachment": [{"name": "head_patch", "time": 0}]}, "head_patch2": {"attachment": [{"name": "head_patch2", "time": 0}]}, "head effect3": {"attachment": [{"name": null, "time": 0}]}, "head effect2": {"attachment": [{"name": "head effect", "time": 0}]}}, "bones": {"head": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 5}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 5}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 5}]}, "head effect": {"rotate": [{"curve": "stepped", "angle": 3.48, "time": 0}, {"angle": 3.48, "time": 5}], "scale": [{"curve": "stepped", "x": 3.767, "y": 1, "time": 0}, {"curve": "stepped", "x": 3.767, "y": 1, "time": 0.8333}, {"curve": "stepped", "x": 3.767, "y": 1, "time": 1.3333}, {"x": 3.767, "y": 1, "time": 3.1667}, {"x": 0.569, "y": 1, "time": 3.8333}, {"x": 1, "y": 1, "time": 5}], "translate": [{"curve": "stepped", "x": -596.58, "y": -124.08, "time": 0}, {"x": -596.58, "y": -124.08, "time": 5}]}, "head effect2": {"rotate": [{"curve": "stepped", "angle": 3.48, "time": 0}, {"angle": 3.48, "time": 5}], "scale": [{"curve": "stepped", "x": 3.967, "y": 1, "time": 0}, {"curve": "stepped", "x": 3.967, "y": 1, "time": 0.8333}, {"x": 3.967, "y": 1, "time": 1.3333}, {"x": 3.767, "y": 1, "time": 3.1667}, {"x": 0.477, "y": 1, "time": 3.8333}, {"x": 1, "y": 1, "time": 5}], "translate": [{"curve": "stepped", "x": -596.58, "y": -124.08, "time": 0}, {"x": -596.58, "y": -124.08, "time": 5}]}}, "paths": {"head_patch": {"position": [{"time": 0, "position": -0.1538}, {"time": 5, "position": 1.1625}]}, "head_patch2": {"position": [{"time": 0, "position": -0.2222}, {"time": 5, "position": 1.1624}]}}, "deform": {"default": {"head_patch": {"head_patch": [{"vertices": [-4.97346, -2.15625, -4.97346, -2.15625, -4.97346, -2.15625], "time": 0}]}}}}, "chon game3": {"slots": {"Chon game": {"attachment": [{"name": "Chon game", "time": 0}, {"name": "Chon game", "time": 1}, {"name": "Chon game", "time": 2}]}, "Chon game_patch": {"attachment": [{"name": "Chon game_patch", "time": 0}, {"name": "Chon game_patch", "time": 0.1}, {"name": "Chon game_patch", "time": 2}]}, "effect loe sang - Copy": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "curve": "stepped", "time": 0.1}, {"color": "ffffffff", "time": 1.2}, {"color": "ffffff00", "time": 1.3333}], "attachment": [{"name": "effect loe sang - <PERSON><PERSON>", "time": 0}, {"name": "effect loe sang - <PERSON><PERSON>", "time": 0.1}]}, "effect xanh": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}, {"color": "ffffff74", "time": 1}, {"color": "ffffffff", "time": 1.5}, {"color": "ffffff00", "time": 2}], "attachment": [{"name": "effect xanh", "time": 0}, {"name": "effect xanh", "time": 1}, {"name": "effect xanh", "time": 2}]}, "Chon game4": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffff9f", "time": 1}, {"color": "ffffff00", "time": 2}], "attachment": [{"name": "Chon game", "time": 0}, {"name": "Chon game", "time": 1}, {"name": null, "time": 2}]}}, "bones": {"Chon game": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}, "Chon game1": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}, "effect loe sang - Copy": {"scale": [{"x": 0.523, "y": 0.523, "time": 0.1}]}, "effect xanh": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}}, "paths": {"Chon game_patch": {"position": [{"curve": "stepped", "time": 0}, {"time": 0.1}, {"curve": "stepped", "time": 1.3333, "position": 1}, {"time": 2, "position": 1}]}}}, "nhiem vu": {"slots": {"nhiem vu 3": {"attachment": [{"name": "nhiem vu 3", "time": 0.3667}, {"name": null, "time": 0.4667}]}, "nhiem vu 2": {"attachment": [{"name": "nhiem vu 2", "time": 0.2667}]}, "nhiem vu 1": {"attachment": [{"name": "nhiem vu 1", "time": 0.1333}, {"name": null, "time": 0.2667}]}, "nhiem vu": {"attachment": [{"name": "nhiem vu", "time": 0}, {"name": "nhiem vu", "time": 1}, {"name": "nhiem vu", "time": 2}]}}, "bones": {"nhiem vu2": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}, "nhiem vu": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 2}]}}}}}