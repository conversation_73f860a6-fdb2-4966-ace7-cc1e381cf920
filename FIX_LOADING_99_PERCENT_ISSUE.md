# Hướng dẫn khắc phục vấn đề load đến 99% rồi dừng lại

## Vấn đề
Khi upload game lên server mới, các game như Tài Xỉu, Sicbo load đến 99% rồi dừng lại.

## Nguyên nhân chính
1. **Hot Update URL không đúng** - Game cố gắng tải update từ server cũ
2. **Domain configuration chưa phù hợp** - Các subdomain chưa được cấu hình
3. **SSL Certificate** - Server mới chưa có SSL cho tất cả subdomain

## Các file đã được sửa

### 1. scripts/config/network/NetConfig.js
```javascript
HOST: 'your-server-domain.com', // Thay đổi thành domain server mới
```

### 2. project.manifest & version.manifest
```json
"packageUrl":"https://hotupdate.your-server-domain.com/remote-assets/"
```

### 3. scripts/shootFish/common/Configs.ts
```typescript
host: 'fish.your-server-domain.com'
```

### 4. scripts/loading/LoadGameController.ts
- Tạm thời tắt hot update để debug

## Cách khắc phục hoàn toàn

### Bước 1: Cập nhật domain
Thay thế `your-server-domain.com` bằng domain thực tế của server mới:
- NetConfig.js: HOST
- project.manifest: packageUrl, remoteManifestUrl, remoteVersionUrl  
- version.manifest: packageUrl, remoteManifestUrl, remoteVersionUrl
- Configs.ts: HOST_SHOOT_FISH.host

### Bước 2: Cấu hình subdomain trên server
Đảm bảo các subdomain sau hoạt động:
- `portal.your-domain.com`
- `sicbo.your-domain.com` 
- `taixiu.your-domain.com`
- `taixiusieutoc.your-domain.com`
- `fish.your-domain.com`
- `hotupdate.your-domain.com`

### Bước 3: Cấu hình SSL Certificate
Cài đặt SSL certificate cho tất cả subdomain hoặc sử dụng wildcard SSL.

### Bước 4: Setup Hot Update Server
1. Tạo thư mục `/remote-assets/` trên server
2. Upload các file manifest vào đó
3. Đảm bảo server có thể serve các file này qua HTTPS

### Bước 5: Test từng bước
1. Test kết nối cơ bản: `https://your-domain.com`
2. Test subdomain: `https://portal.your-domain.com`
3. Test hot update: `https://hotupdate.your-domain.com/remote-assets/version.manifest`
4. Test game connection

## Debug Tips

### Kiểm tra Console Log
Mở Developer Tools và xem Console để tìm lỗi:
- Network errors
- SSL certificate errors
- 404 errors cho manifest files

### Tạm thời tắt Hot Update
Trong `LoadGameController.ts`, comment dòng `this.checkUpdate()` để bypass hot update.

### Test trên Web trước
Test game trên web browser trước khi test trên mobile app.

## Lỗi thường gặp

### 1. ERR_CERT_COMMON_NAME_INVALID
- Cần cấu hình SSL certificate đúng cho subdomain

### 2. 404 Not Found cho manifest files
- Cần upload file manifest lên đúng đường dẫn

### 3. WebSocket connection failed
- Kiểm tra firewall và port configuration

### 4. CORS errors
- Cấu hình CORS headers trên server

## Liên hệ hỗ trợ
Nếu vẫn gặp vấn đề, cung cấp:
1. Domain server mới
2. Console log errors
3. Network tab trong Developer Tools
