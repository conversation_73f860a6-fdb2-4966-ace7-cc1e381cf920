{"skeleton": {"hash": "vwSKS4yXzIUyFxttKrr2hwkKRjg", "spine": "3.6.53", "width": 500.5, "height": 478.4, "images": "./images/"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 211.65, "rotation": 91.45, "x": 7.76, "y": 5.29}, {"name": "bungchim", "parent": "bone", "length": 81.19, "rotation": 0.7, "x": 26.04, "y": -12.36}, {"name": "dauchim", "parent": "bungchim", "length": 80.54, "rotation": -0.85, "x": 86.86, "y": 4.67}, {"name": "Layer 32", "parent": "dauchim", "length": 28.26, "rotation": 12.43, "x": 45.68, "y": -30.33}, {"name": "chansau", "parent": "bone", "length": 41.64, "rotation": 173.5, "x": 64.54, "y": 37.92}, {"name": "banchan", "parent": "chansau", "length": 13.54, "rotation": -49.12, "x": 52.08, "y": -0.3}, {"name": "<PERSON><PERSON><PERSON>", "parent": "bone", "length": 33.02, "rotation": -169.73, "x": 45.76, "y": -28.73}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parent": "<PERSON><PERSON><PERSON>", "length": 18.2, "rotation": 64.7, "x": 36.85, "y": 0.45}, {"name": "bungchim3", "parent": "bungchim", "length": 52.32, "rotation": -178.53, "x": 53.84, "y": -10.11}, {"name": "chamchim", "parent": "root", "x": 12.33, "y": 9.14}, {"name": "chym1", "parent": "root", "x": 18.65, "y": 34.67, "scaleX": 1.3, "scaleY": 1.3}, {"name": "nenmat1", "parent": "dauchim", "length": 39.67, "rotation": -3.06, "x": 37.1, "y": 21.73}, {"name": "cuongmat1", "parent": "nenmat1", "length": 17.6, "rotation": -40.7, "x": 6.19, "y": 6.13}, {"name": "loimat1", "parent": "cuongmat1", "x": 7.86, "y": -0.64}, {"name": "mongmat2", "parent": "Layer 32", "length": 17.04, "rotation": -10.64, "x": -0.79, "y": 6.83}, {"name": "loimat2", "parent": "mongmat2", "x": 7.57, "y": 0.75}, {"name": "machim", "parent": "dauchim", "length": 42.37, "rotation": 5.31, "x": -5.09, "y": -9.04}, {"name": "mimat1", "parent": "nenmat1", "length": 20.03, "rotation": -175.61, "x": 28.14, "y": -1.99}, {"name": "mimat2", "parent": "Layer 32", "length": 14.17, "rotation": 179.4, "x": 19.67, "y": -1.48}, {"name": "<PERSON><PERSON><PERSON>", "parent": "machim", "length": 28.68, "rotation": -157.32, "x": 47.43, "y": 1.26}, {"name": "<PERSON><PERSON>n", "parent": "machim", "length": 11.51, "rotation": -154.61, "x": 51.93, "y": -2.34}, {"name": "taysauchim", "parent": "bungchim", "length": 42.48, "rotation": -161.12, "x": 82.59, "y": -27.52}, {"name": "taysauchim3", "parent": "taysauchim", "length": 46.77, "rotation": 120.67, "x": 2.46, "y": -0.82}, {"name": "taytruochim1", "parent": "bungchim", "length": 38.11, "rotation": 138.04, "x": 86.02, "y": 47.43}, {"name": "taytruochim3", "parent": "taytruochim1", "length": 24.13, "rotation": -160.92, "x": 40.3, "y": -9.29}, {"name": "taytruochim5", "parent": "taytruochim3", "length": 27.23, "rotation": -143.68, "x": 40.43, "y": -6.88}, {"name": "taytruochim6", "parent": "taytruochim3", "length": 38.2, "rotation": 40.32, "x": 47.12, "y": -0.43}], "slots": [{"name": "chamchim", "bone": "chamchim", "attachment": "chamchim"}, {"name": "taytruochim1", "bone": "taytruochim1", "attachment": "taytruochim1"}, {"name": "taysauchim2", "bone": "taysauchim3", "attachment": "taysauchim2"}, {"name": "taysauchim", "bone": "taysauchim", "attachment": "taysauchim"}, {"name": "taytruochim4", "bone": "taytruochim6", "attachment": "taytruochim4"}, {"name": "banchan", "bone": "banchan", "attachment": "banchan"}, {"name": "chansau", "bone": "chansau", "attachment": "chansau"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "dauchim", "bone": "dauchim", "attachment": "dauchim"}, {"name": "cuongmat1", "bone": "cuongmat1", "attachment": "cuongmat1"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "loimat1", "bone": "loimat1", "attachment": "loimat1"}, {"name": "nenmat1", "bone": "nenmat1", "attachment": "nenmat1"}, {"name": "mimat1", "bone": "mimat1"}, {"name": "bungchim", "bone": "bungchim3", "attachment": "bungchim"}, {"name": "taytruochim2", "bone": "taytruochim3", "attachment": "taytruochim2"}, {"name": "mongmat2", "bone": "mongmat2", "attachment": "mongmat2"}, {"name": "loimat2", "bone": "loimat2", "attachment": "loimat2"}, {"name": "Layer 32", "bone": "Layer 32", "attachment": "Layer 32"}, {"name": "machim", "bone": "machim", "attachment": "machim"}, {"name": "taytruochim3", "bone": "taytruochim5", "attachment": "taytruochim3"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>n", "bone": "<PERSON><PERSON>n", "attachment": "<PERSON><PERSON>n"}, {"name": "mimat2", "bone": "mimat2"}, {"name": "chym1", "bone": "chym1", "attachment": "chym1"}], "skins": {"default": {"Layer 32": {"Layer 32": {"x": 13.91, "y": 1.27, "rotation": -103.74, "width": 34, "height": 45}}, "banchan": {"banchan": {"x": 11.37, "y": -0.6, "rotation": 144.16, "width": 43, "height": 21}}, "banchansau": {"banchansau": {"x": 8.51, "y": 1.07, "rotation": 13.57, "width": 45, "height": 19}}, "bungchim": {"bungchim": {"x": 0.78, "y": -19.36, "rotation": 86.38, "width": 102, "height": 117}}, "chamchim": {"chamchim": {"x": -5.07, "y": 0.18, "width": 102, "height": 36}}, "chansau": {"chansau": {"x": 17.98, "y": -4.38, "rotation": 95.04, "width": 50, "height": 74}}, "chym1": {"chym1": {"x": 5.83, "y": 144.36, "width": 385, "height": 368}, "chym2": {"x": 5.83, "y": 144.36, "width": 385, "height": 368}, "chym3": {"x": 5.83, "y": 144.36, "width": 385, "height": 368}, "chym4": {"x": 5.83, "y": 144.36, "width": 385, "height": 368}, "chym5": {"x": 5.83, "y": 144.36, "width": 385, "height": 368}, "chym6": {"x": 5.83, "y": 144.36, "width": 385, "height": 368}, "chym7": {"x": 5.83, "y": 144.36, "width": 385, "height": 368}, "chym8": {"x": 5.83, "y": 144.36, "width": 385, "height": 368}}, "cuongmat1": {"cuongmat1": {"x": 12.52, "y": -2.43, "rotation": -47.53, "width": 34, "height": 31}}, "dauchim": {"dauchim": {"x": 45.79, "y": 9.22, "rotation": -91.3, "width": 116, "height": 119}}, "duisau": {"duisau": {"x": 10.34, "y": 0.87, "rotation": 78.27, "width": 44, "height": 55}}, "loimat1": {"loimat1": {"x": 0.12, "y": -0.54, "rotation": -47.53, "width": 20, "height": 20}}, "loimat2": {"loimat2": {"x": 0.05, "y": -0.57, "rotation": -93.09, "width": 19, "height": 19}}, "machim": {"machim": {"x": 22.29, "y": 10.41, "rotation": -96.61, "width": 105, "height": 70}}, "mimat1": {"mimat1": {"x": 11.54, "y": -1.57, "rotation": 87.37, "width": 37, "height": 26}}, "mimat2": {"mimat2": {"x": 9.88, "y": -0.57, "rotation": 76.87, "width": 22, "height": 22}}, "momduoi": {"momduoi": {"x": 12.68, "y": -5.04, "rotation": 60.71, "width": 53, "height": 39}}, "momtren": {"momtren": {"x": 3.71, "y": -1.43, "rotation": 57.99, "width": 43, "height": 16}}, "mongmat2": {"mongmat2": {"x": 9.56, "y": -0.93, "rotation": -93.09, "width": 23, "height": 27}}, "nenmat1": {"nenmat1": {"x": 20.45, "y": 0.82, "rotation": -88.24, "width": 55, "height": 50}}, "taysauchim": {"taysauchim": {"x": 20.68, "y": -1.62, "rotation": 68.96, "width": 43, "height": 64}}, "taysauchim2": {"taysauchim2": {"x": 45.58, "y": 12.13, "rotation": -51.71, "width": 51, "height": 106}}, "taytruochim1": {"taytruochim1": {"x": 20.9, "y": -0.88, "rotation": 129.81, "width": 47, "height": 54}}, "taytruochim2": {"taytruochim2": {"x": 11.64, "y": -6.7, "rotation": -69.27, "width": 30, "height": 46}}, "taytruochim3": {"taytruochim3": {"x": 22.36, "y": 0.66, "rotation": 74.41, "width": 40, "height": 57}}, "taytruochim4": {"taytruochim4": {"x": 28.64, "y": 1.16, "rotation": -109.59, "width": 82, "height": 124}}}}, "animations": {"chimanline": {"slots": {"chym1": {"attachment": [{"time": 0, "name": null}]}, "mimat1": {"color": [{"time": 0, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "mimat1"}]}, "mimat2": {"color": [{"time": 0, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "mimat2"}]}, "taysauchim": {"attachment": [{"time": 0, "name": null}]}, "taysauchim2": {"attachment": [{"time": 0, "name": "taysauchim2"}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 57.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 0, "y": 1.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 0, "y": 57.56}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "taytruochim6": {"rotate": [{"time": 0, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 6.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "taytruochim5": {"rotate": [{"time": 0, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -1.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "taytruochim3": {"rotate": [{"time": 0, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 20.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "taytruochim1": {"rotate": [{"time": 0, "angle": 13.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 17.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 14.34}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "taysauchim3": {"rotate": [{"time": 0, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -118.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": -0.85}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": -1.84, "y": 1.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "taysauchim": {"rotate": [{"time": 0, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "momtren": {"rotate": [{"time": 0, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -1.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": -1.01, "y": 0.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 1.086, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "momduoi": {"rotate": [{"time": 0, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 5.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": -0.75, "y": -0.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 0.843, "y": 0.986, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "machim": {"rotate": [{"time": 0, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": -1.09, "y": 0.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 1.047, "y": 1.022, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "banchansau": {"rotate": [{"time": 0, "angle": -30.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": -29.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 14.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -29.22, "curve": "stepped"}, {"time": 0.8, "angle": -29.22}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 1.059, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "duisau": {"rotate": [{"time": 0, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -6.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 2.53, "y": -0.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 0.879, "y": 1.066, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "mimat1": {"rotate": [{"time": 0, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "loimat1": {"rotate": [{"time": 0, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 6.43, "y": 5.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 6.43, "y": 5.89}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "cuongmat1": {"rotate": [{"time": 0, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "nenmat1": {"rotate": [{"time": 0, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "Layer 32": {"rotate": [{"time": 0, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "dauchim": {"rotate": [{"time": 0, "angle": 3.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": -3.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 3.68}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.982, "y": 0.982, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 0.94, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "banchan": {"rotate": [{"time": 0, "angle": 22.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 23.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -8.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 23.7, "curve": "stepped"}, {"time": 0.8, "angle": 23.7}], "translate": [{"time": 0, "x": -1.15, "y": 0.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": -0.8, "y": 0.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": -1.15, "y": 0.45}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 1.188, "y": 1.086, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "chansau": {"rotate": [{"time": 0, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 1.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": -3.37, "y": -3.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": -2.84, "y": -7.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": -2, "y": -7.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": -9.15, "y": -8.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": -3.37, "y": -3.2}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 0.76, "y": 1.007, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "bungchim": {"rotate": [{"time": 0, "angle": -0.85, "curve": "stepped"}, {"time": 0.8, "angle": -0.85}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": -5.03, "y": 0.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 0.871, "y": 0.962, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "loimat2": {"rotate": [{"time": 0, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 7.07, "y": -0.38, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 7.07, "y": -0.38}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "chamchim": {"rotate": [{"time": 0, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 0.926, "y": 0.891, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "bungchim3": {"rotate": [{"time": 0, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 1.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 6.54, "y": -0.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 0.894, "y": 1.022, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "mimat2": {"rotate": [{"time": 0, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "mongmat2": {"rotate": [{"time": 0, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "chym1": {"rotate": [{"time": 0, "angle": -0.85, "curve": "stepped"}, {"time": 0.8, "angle": -0.85}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}}}, "chimhieuung": {"slots": {"Layer 32": {"attachment": [{"time": 0, "name": null}]}, "banchan": {"attachment": [{"time": 0, "name": null}]}, "banchansau": {"attachment": [{"time": 0, "name": null}]}, "bungchim": {"attachment": [{"time": 0, "name": null}]}, "chamchim": {"attachment": [{"time": 0, "name": null}]}, "chansau": {"attachment": [{"time": 0, "name": null}]}, "chym1": {"attachment": [{"time": 0, "name": "chym1"}, {"time": 0.0667, "name": "chym2"}, {"time": 0.1333, "name": "chym3"}, {"time": 0.2333, "name": "chym4"}, {"time": 0.3333, "name": "chym5"}, {"time": 0.4333, "name": "chym6"}, {"time": 0.5333, "name": "chym7"}, {"time": 0.6, "name": "chym8"}, {"time": 1, "name": "chym1"}, {"time": 1.1, "name": "chym1"}]}, "cuongmat1": {"attachment": [{"time": 0, "name": null}]}, "dauchim": {"attachment": [{"time": 0, "name": null}]}, "duisau": {"attachment": [{"time": 0, "name": null}]}, "loimat1": {"attachment": [{"time": 0, "name": null}]}, "loimat2": {"attachment": [{"time": 0, "name": null}]}, "machim": {"attachment": [{"time": 0, "name": null}]}, "momduoi": {"attachment": [{"time": 0, "name": null}]}, "momtren": {"attachment": [{"time": 0, "name": null}]}, "mongmat2": {"attachment": [{"time": 0, "name": null}]}, "nenmat1": {"attachment": [{"time": 0, "name": null}]}, "taysauchim": {"attachment": [{"time": 0, "name": null}]}, "taysauchim2": {"attachment": [{"time": 0, "name": null}]}, "taytruochim1": {"attachment": [{"time": 0, "name": null}]}, "taytruochim2": {"attachment": [{"time": 0, "name": null}]}, "taytruochim3": {"attachment": [{"time": 0, "name": null}]}, "taytruochim4": {"attachment": [{"time": 0, "name": null}]}}, "bones": {"chym1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 5.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 0, "curve": "stepped"}, {"time": 0.4333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": -5.16, "curve": "stepped"}, {"time": 0.9333, "angle": -5.16, "curve": [0.247, 0, 0.63, 0.52]}, {"time": 1.1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.324, 0, 0.657, 0.34]}, {"time": 0.6, "x": 232.66, "y": 50.97, "curve": "stepped"}, {"time": 0.8333, "x": 232.66, "y": 50.97, "curve": [0.297, 0.11, 0.635, 0.47]}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "x": 0.916, "y": 0.888, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 0.1667, "x": 0.969, "y": 1.141, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 0.2333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 0.916, "y": 0.972, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 1, "y": 0.972, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 0.5333, "x": 1.154, "y": 1.154, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 0.6, "x": 1.385, "y": 1.392, "curve": "stepped"}, {"time": 0.9333, "x": 1.385, "y": 1.392, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1, "x": 0, "y": 0}]}}}, "chimthuong": {"slots": {"chym1": {"attachment": [{"time": 0, "name": null}]}, "mimat1": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffff00"}, {"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "mimat1"}]}, "mimat2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffff00"}, {"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "mimat2"}]}, "taysauchim2": {"attachment": [{"time": 0, "name": null}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 1.015, "y": 1}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "taytruochim6": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 0.973, "y": 0.965}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "taytruochim5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 0.965, "y": 0.974}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "taytruochim3": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4333, "angle": 10.23}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "taytruochim1": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4, "angle": 8.19}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "taysauchim3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "taysauchim": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2, "angle": -8.37}, {"time": 0.4, "angle": 0}, {"time": 0.5667, "angle": -8.37}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "momtren": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.4, "x": -1.01, "y": 0.12}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 1.035, "y": 1}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "momduoi": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.4, "x": -0.75, "y": -0.61}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 0.927, "y": 0.986}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "machim": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.2, "x": -0.84, "y": 0.03}, {"time": 0.4, "x": 0, "y": 0}, {"time": 0.6, "x": -0.84, "y": 0.03}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.2, "x": 1.027, "y": 1}, {"time": 0.4, "x": 1, "y": 1}, {"time": 0.6, "x": 1.027, "y": 1}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "banchansau": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4333, "angle": 3.83}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "duisau": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.4, "x": 1.95, "y": -0.05}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 1, "y": 0.96}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "mimat1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.2, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "x": 0.5, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "loimat1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.2, "x": -0.61, "y": 0.67}, {"time": 0.2667, "x": 0, "y": 0}, {"time": 0.4, "x": 0.2, "y": -0.22}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.6333, "x": 1.03, "y": 0.77}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "cuongmat1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "nenmat1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 1.037, "y": 1}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "Layer 32": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "dauchim": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4, "angle": 3.19}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.4, "x": 0.62, "y": -1.35}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 1, "y": 0.993}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "banchan": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4, "angle": 4.87}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "chansau": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4, "angle": -2.66}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.4, "x": -0.05, "y": -1.98}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 1.028, "y": 1}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "bungchim": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.4, "x": -2.3, "y": 0.06}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 0.974, "y": 1}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "loimat2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.2, "x": -0.09, "y": -1.63}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.4667, "x": 1.4, "y": -0.98}, {"time": 0.5667, "x": 0, "y": 0}, {"time": 0.6333, "x": 0.8, "y": 1.46}, {"time": 0.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "chamchim": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 0.947, "y": 0.855}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "bungchim3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.4, "x": -2.67, "y": 0.1}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 1, "y": 1.027}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "mimat2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.2, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "x": 0.5, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}}}}}