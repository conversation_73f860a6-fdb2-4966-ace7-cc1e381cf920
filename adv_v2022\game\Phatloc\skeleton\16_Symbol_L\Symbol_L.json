{"skeleton": {"hash": "fRHwfqrA3WITiJE+yi5oc+X98ug", "spine": "3.7.94", "width": 241.87, "height": 241.87, "images": "./images/", "audio": "D:/2020/FTGong/Animation/FTG_Gong/Export"}, "bones": [{"name": "root"}, {"name": "Scale_720", "parent": "root", "scaleX": 0.67, "scaleY": 0.67}, {"name": "<PERSON>", "parent": "Scale_720", "color": "2400ffff"}, {"name": "Wild", "parent": "Scale_720"}, {"name": "GongLight", "parent": "Scale_720", "rotation": 14.94}, {"name": "TextGong", "parent": "Scale_720"}, {"name": "Fotune", "parent": "TextGong", "y": 15.17}, {"name": "HHy", "parent": "Fotune", "x": 10.41, "y": 5, "color": "00fbffff"}, {"name": "YHy", "parent": "Fotune", "x": 30.41, "y": 5, "color": "00fbffff"}, {"name": "Sparkle", "parent": "YHy", "x": 95.79, "y": 36.57, "color": "00fbffff"}, {"name": "Sparkle2", "parent": "HHy", "x": 64.42, "y": 9.39, "color": "00fbffff"}, {"name": "<PERSON><PERSON>", "parent": "Fotune", "x": -29.59, "y": 5, "color": "00fbffff"}, {"name": "Sparkle3", "parent": "<PERSON><PERSON>", "x": -65.29, "y": 15.26, "color": "00fbffff"}, {"name": "UCung", "parent": "Fotune", "x": -19.59, "y": 5, "color": "00fbffff"}, {"name": "Sparkle4", "parent": "UCung", "x": -39.05, "y": 56.23, "color": "00fbffff"}, {"name": "NCung", "parent": "Fotune", "x": -9.59, "y": 5, "color": "00fbffff"}, {"name": "Sparkle5", "parent": "NCung", "x": -5.46, "y": 22.61, "color": "00fbffff"}, {"name": "Sparkle6", "parent": "HHy", "x": 38.47, "y": 45.38, "color": "00fbffff"}, {"name": "Sparkle7", "parent": "<PERSON><PERSON>", "x": -93.06, "y": 39.17, "color": "00fbffff"}, {"name": "Fotune2", "parent": "TextGong", "y": -51.84}, {"name": "APhat", "parent": "Fotune2", "x": -9.59, "y": 60, "color": "00fbffff"}, {"name": "Sparkle8", "parent": "APhat", "x": -26.5, "y": -5.98, "color": "00fbffff"}, {"name": "TTai", "parent": "Fotune2", "x": 10.41, "y": 60, "color": "00fbffff"}, {"name": "Sparkle9", "parent": "TTai", "x": 31.56, "y": -6.74, "color": "00fbffff"}, {"name": "PPhat", "parent": "Fotune2", "x": -29.59, "y": 60, "color": "00fbffff"}, {"name": "Sparkle10", "parent": "PPhat", "x": -104, "y": -67.22, "color": "00fbffff"}, {"name": "ATai", "parent": "Fotune2", "x": 20.41, "y": 60, "color": "00fbffff"}, {"name": "Sparkle11", "parent": "ATai", "x": 79.91, "y": -66.32, "color": "00fbffff"}, {"name": "GCung", "parent": "Fotune", "x": 0.41, "y": 5, "color": "00fbffff"}, {"name": "N1", "parent": "Fotune", "x": 20.41, "y": 5, "color": "00fbffff"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parent": "Fotune", "x": 41.13, "y": 5, "color": "00fbffff"}, {"name": "HPhat", "parent": "Fotune2", "x": -19.59, "y": 60, "color": "00fbffff"}, {"name": "ITai", "parent": "Fotune2", "x": 30.41, "y": 60, "color": "00fbffff"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "Fotune2", "x": 41.13, "y": 60, "color": "00fbffff"}, {"name": "TPhat", "parent": "Fotune2", "x": 0.41, "y": 60, "color": "00fbffff"}, {"name": "DauSac", "parent": "Fotune2", "x": 0.41, "y": 60, "color": "00fbffff"}], "slots": [{"name": "<PERSON>", "bone": "<PERSON>", "attachment": "<PERSON>"}, {"name": "GongLight", "bone": "GongLight", "attachment": "GongLight"}, {"name": "Wild", "bone": "Wild", "attachment": "Wild"}, {"name": "CCungShadow", "bone": "<PERSON><PERSON>", "color": "ffffffbf", "attachment": "CCungShadow"}, {"name": "UCungShadow", "bone": "UCung", "color": "ffffffbf", "attachment": "UCungShadow"}, {"name": "NCungShadow", "bone": "NCung", "color": "ffffffbf", "attachment": "NCungShadow"}, {"name": "YHyShadow", "bone": "YHy", "color": "ffffffbf", "attachment": "YHyShadow"}, {"name": "HHyShadow", "bone": "HHy", "color": "ffffffbf", "attachment": "HHyShadow"}, {"name": "PPhatShadow", "bone": "PPhat", "color": "ffffffbf", "attachment": "PPhatShadow"}, {"name": "ITaiShadow", "bone": "ITai", "color": "ffffffbf", "attachment": "ITaiShadow"}, {"name": "ATaiShadow", "bone": "ATai", "color": "ffffffbf", "attachment": "ATaiShadow"}, {"name": "TTaiShadow", "bone": "TTai", "color": "ffffffbf", "attachment": "TTaiShadow"}, {"name": "HPhatShadow", "bone": "HPhat", "color": "ffffffbf", "attachment": "HPhatShadow"}, {"name": "APhatShadow", "bone": "APhat", "color": "ffffffbf", "attachment": "APhatShadow"}, {"name": "GCungShadow", "bone": "GCung", "color": "ffffffbf", "attachment": "GCungShadow"}, {"name": "DauSacShadow", "bone": "DauSac", "attachment": "DauSacShadow"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "DauHoiLight", "bone": "<PERSON><PERSON><PERSON><PERSON>", "attachment": "DauHoiLight"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "DauHuyenLight", "bone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attachment": "DauHuyenLight"}, {"name": "TPhatShadow", "bone": "TPhat", "attachment": "TPhatShadow"}, {"name": "YHy", "bone": "YHy", "attachment": "YHy"}, {"name": "YHyLight", "bone": "YHy", "attachment": "YHyLight"}, {"name": "ITai", "bone": "ITai", "attachment": "ITai"}, {"name": "ITaiLight", "bone": "ITai", "attachment": "ITaiLight"}, {"name": "ATai", "bone": "ATai", "attachment": "ATai"}, {"name": "ATaiLight", "bone": "ATai", "attachment": "ATaiLight"}, {"name": "HHy", "bone": "HHy", "attachment": "HHy"}, {"name": "HHyLight", "bone": "HHy", "attachment": "HHyLight"}, {"name": "TTai", "bone": "TTai", "attachment": "TTai"}, {"name": "TTaiLight", "bone": "TTai", "attachment": "TTaiLight"}, {"name": "<PERSON><PERSON>", "bone": "<PERSON><PERSON>", "attachment": "<PERSON><PERSON>"}, {"name": "CCungLight", "bone": "<PERSON><PERSON>", "attachment": "CCungLight"}, {"name": "PPhat", "bone": "PPhat", "attachment": "PPhat"}, {"name": "PPhatLight", "bone": "PPhat", "attachment": "PPhatLight"}, {"name": "UCung", "bone": "UCung", "attachment": "UCung"}, {"name": "UCungLight", "bone": "UCung", "attachment": "UCungLight"}, {"name": "HPhat", "bone": "HPhat", "attachment": "HPhat"}, {"name": "HPhatLight", "bone": "HPhat", "attachment": "HPhatLight"}, {"name": "NCung", "bone": "NCung", "attachment": "NCung"}, {"name": "NCungLight", "bone": "NCung", "attachment": "NCungLight"}, {"name": "APhat", "bone": "APhat", "attachment": "APhat"}, {"name": "APhatLight", "bone": "APhat", "attachment": "APhatLight"}, {"name": "GCung", "bone": "GCung", "attachment": "GCung"}, {"name": "GCungLight", "bone": "GCung", "attachment": "GCungLight"}, {"name": "TPhat", "bone": "TPhat", "attachment": "TPhat"}, {"name": "TPhatLight", "bone": "TPhat", "attachment": "TPhatLight"}, {"name": "DauSac", "bone": "DauSac", "attachment": "DauSac"}, {"name": "DauSacLight", "bone": "DauSac", "attachment": "DauSacLight"}, {"name": "Sparkle", "bone": "Sparkle", "attachment": "Sparkle"}, {"name": "Sparkle7", "bone": "Sparkle7", "attachment": "Sparkle"}, {"name": "Sparkle2", "bone": "Sparkle2", "attachment": "Sparkle"}, {"name": "Sparkle3", "bone": "Sparkle3", "attachment": "Sparkle"}, {"name": "Sparkle4", "bone": "Sparkle4", "attachment": "Sparkle"}, {"name": "Sparkle6", "bone": "Sparkle6", "attachment": "Sparkle"}, {"name": "Sparkle10", "bone": "Sparkle10", "attachment": "Sparkle"}, {"name": "Sparkle11", "bone": "Sparkle11", "attachment": "Sparkle"}, {"name": "Sparkle5", "bone": "Sparkle5", "attachment": "Sparkle"}, {"name": "Sparkle8", "bone": "Sparkle8", "attachment": "Sparkle"}, {"name": "Sparkle9", "bone": "Sparkle9", "attachment": "Sparkle"}], "skins": {"default": {"APhat": {"APhat": {"x": -31.45, "y": -33.26, "width": 57, "height": 66}}, "APhatLight": {"APhatLight": {"x": -31.45, "y": -33.26, "width": 57, "height": 66}}, "APhatShadow": {"APhatShadow": {"x": -27.45, "y": -43.64, "width": 71, "height": 82}}, "ATai": {"ATai": {"x": 81.04, "y": -39.99, "width": 63, "height": 63}}, "ATaiLight": {"ATaiLight": {"x": 81.04, "y": -39.99, "width": 63, "height": 63}}, "ATaiShadow": {"ATaiShadow": {"x": 81.04, "y": -51.98, "width": 76, "height": 79}}, "CCung": {"CCung": {"x": -76.13, "y": 21.02, "width": 47, "height": 49}}, "CCungLight": {"CCungLight": {"x": -76.13, "y": 21.02, "width": 47, "height": 49}}, "CCungShadow": {"CCungShadow": {"x": -79.32, "y": 9.03, "width": 62, "height": 65}}, "DauHoi": {"DauHoi": {"x": 65.43, "y": 43.35, "width": 23, "height": 21}}, "DauHoiLight": {"DauHoiLight": {"x": 65.43, "y": 43.35, "width": 23, "height": 21}}, "DauHuyen": {"DauHuyen": {"x": 68.88, "y": -10.1, "width": 27, "height": 17}}, "DauHuyenLight": {"DauHuyenLight": {"x": 68.88, "y": -10.1, "width": 27, "height": 17}}, "DauSac": {"DauSac": {"x": -29.68, "y": 1.95, "width": 18, "height": 14}}, "DauSacLight": {"DauSacLight": {"x": -29.68, "y": 1.95, "width": 22, "height": 20}}, "DauSacShadow": {"DauSacShadow": {"x": -27.77, "y": -5.72, "width": 33, "height": 30}}, "GCung": {"GCung": {"x": 13.28, "y": 37.02, "width": 50, "height": 57}}, "GCungLight": {"GCungLight": {"x": 13.28, "y": 37.02, "width": 50, "height": 57}}, "GCungShadow": {"GCungShadow": {"x": 14.08, "y": 25.03, "width": 66, "height": 73}}, "Gong": {"Gong": {"x": 0.77, "y": -0.48, "width": 361, "height": 361}}, "GongLight": {"GongLight": {"color": "fff0b9ff", "x": -75.31, "y": 88.74, "rotation": -0.75, "width": 78, "height": 74}}, "HHy": {"HHy": {"x": 55.83, "y": 32.99, "width": 48, "height": 53}}, "HHyLight": {"HHyLight": {"x": 55.83, "y": 32.99, "width": 48, "height": 53}}, "HHyShadow": {"HHyShadow": {"x": 52.64, "y": 18.61, "width": 62, "height": 65}}, "HPhat": {"HPhat": {"x": -65.79, "y": -39.15, "width": 57, "height": 66}}, "HPhatLight": {"HPhatLight": {"x": -65.79, "y": -39.15, "width": 57, "height": 66}}, "HPhatShadow": {"HPhatShadow": {"x": -63.4, "y": -51.94, "width": 71, "height": 81}}, "ITai": {"ITai": {"x": 101.8, "y": -50.61, "width": 31, "height": 60}}, "ITaiLight": {"ITaiLight": {"x": 101.8, "y": -50.61, "width": 31, "height": 60}}, "ITaiShadow": {"ITaiShadow": {"x": 101, "y": -59.4, "width": 45, "height": 74}}, "NCung": {"NCung": {"x": -23.86, "y": 36.91, "width": 42, "height": 50}}, "NCungLight": {"NCungLight": {"x": -23.86, "y": 36.91, "width": 42, "height": 50}}, "NCungShadow": {"NCungShadow": {"x": -16.67, "y": 26.53, "width": 57, "height": 66}}, "PPhat": {"PPhat": {"x": -99.51, "y": -46.62, "width": 49, "height": 69}}, "PPhatLight": {"PPhatLight": {"x": -99.51, "y": -46.62, "width": 49, "height": 69}}, "PPhatShadow": {"PPhatShadow": {"x": -90.72, "y": -57.81, "width": 64, "height": 84}}, "Sparkle": {"Sparkle": {"scaleX": 0.7, "scaleY": 0.7, "width": 66, "height": 66}}, "Sparkle10": {"Sparkle": {"scaleX": 0.9, "scaleY": 0.9, "width": 66, "height": 66}}, "Sparkle11": {"Sparkle": {"scaleX": 0.9, "scaleY": 0.9, "width": 66, "height": 66}}, "Sparkle2": {"Sparkle": {"scaleX": 0.8, "scaleY": 0.8, "width": 66, "height": 66}}, "Sparkle3": {"Sparkle": {"scaleX": 0.8, "scaleY": 0.8, "width": 66, "height": 66}}, "Sparkle4": {"Sparkle": {"scaleX": 0.9, "scaleY": 0.9, "width": 66, "height": 66}}, "Sparkle5": {"Sparkle": {"width": 66, "height": 66}}, "Sparkle6": {"Sparkle": {"scaleX": 0.9, "scaleY": 0.9, "width": 66, "height": 66}}, "Sparkle7": {"Sparkle": {"scaleX": 0.7, "scaleY": 0.7, "width": 66, "height": 66}}, "Sparkle8": {"Sparkle": {"width": 66, "height": 66}}, "Sparkle9": {"Sparkle": {"width": 66, "height": 66}}, "TPhat": {"TPhat": {"x": 1.28, "y": -31.89, "width": 49, "height": 60}}, "TPhatLight": {"TPhatLight": {"x": 1.28, "y": -31.89, "width": 49, "height": 60}}, "TPhatShadow": {"TPhatShadow": {"x": -3.32, "y": -40.68, "width": 63, "height": 76}}, "TTai": {"TTai": {"x": 50.12, "y": -33.12, "width": 56, "height": 62}}, "TTaiLight": {"TTaiLight": {"x": 50.12, "y": -33.12, "width": 56, "height": 62}}, "TTaiShadow": {"TTaiShadow": {"x": 47.7, "y": -42.68, "width": 68, "height": 76}}, "UCung": {"UCung": {"x": -51.26, "y": 31.02, "width": 44, "height": 52}}, "UCungLight": {"UCungLight": {"x": -51.26, "y": 31.02, "width": 44, "height": 52}}, "UCungShadow": {"UCungShadow": {"x": -48.86, "y": 19.83, "width": 60, "height": 68}}, "Wild": {"Wild": {"y": 3, "width": 226, "height": 110}}, "YHy": {"YHy": {"x": 70.43, "y": 22.22, "width": 45, "height": 50}}, "YHyLight": {"YHyLight": {"x": 70.43, "y": 22.22, "width": 45, "height": 50}}, "YHyShadow": {"YHyShadow": {"x": 71.23, "y": 11.84, "width": 60, "height": 66}}}}, "animations": {"Idle": {"slots": {"APhatLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.0667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "color": "ffffff9b", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "color": "ffffff64", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9667, "color": "ffffff00"}]}, "ATaiLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "color": "ffffff9b", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "color": "ffffff64", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffff00"}]}, "CCungLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "color": "ffffff9b", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "color": "ffffff64", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffff00"}]}, "DauHoiLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "color": "ffffff9b", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "color": "ffffff64", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffff00"}]}, "DauHuyenLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "color": "ffffff9b", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "color": "ffffff64", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffff00"}]}, "DauSacLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.0333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "color": "ffffff9b", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "color": "ffffff64", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9333, "color": "ffffff00"}]}, "GCungLight": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "color": "ffffff9b", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "color": "ffffff64", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9, "color": "ffffff00"}]}, "GongLight": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "color": "ffffff00"}]}, "HPhatLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "color": "ffffff9b", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "color": "ffffff64", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffff00"}]}, "ITaiLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "color": "ffffff9b", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "color": "ffffff64", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffff00"}]}, "NCungLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.0667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "color": "ffffff9b", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "color": "ffffff64", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9667, "color": "ffffff00"}]}, "PPhatLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "color": "ffffff9b", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "color": "ffffff64", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffff00"}]}, "TPhatLight": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "color": "ffffff9b", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "color": "ffffff64", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9, "color": "ffffff00"}]}, "TTaiLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.0667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "color": "ffffff9b", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "color": "ffffff64", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9667, "color": "ffffff00"}]}, "UCungLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "color": "ffffff9b", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "color": "ffffff64", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffff00"}]}, "Wild": {"attachment": [{"time": 0, "name": null}]}, "YHyLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "color": "ffffff9b", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "color": "ffffff64", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9667, "color": "ffffff00"}]}}, "bones": {"GongLight": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -96.69}]}, "HHy": {"translate": [{"time": 0.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 0, "y": -5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 0, "y": 4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": -3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7, "x": 0, "y": -1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "x": 0.8, "y": 0.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "x": 0.9, "y": 0.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "x": 1.05, "y": 1.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9667, "x": 1, "y": 1}]}, "GCung": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 0, "y": -5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 0, "y": 4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "x": 0, "y": -3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 0, "y": 2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "x": 0, "y": -1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0.8, "y": 0.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "x": 0.9, "y": 0.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1.05, "y": 1.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9, "x": 1, "y": 1}]}, "NCung": {"translate": [{"time": 0.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 0, "y": -5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 0, "y": 4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": -3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7, "x": 0, "y": -1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "x": 0.8, "y": 0.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "x": 0.9, "y": 0.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "x": 1.05, "y": 1.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9667, "x": 1, "y": 1}]}, "UCung": {"translate": [{"time": 0.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 0, "y": -5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 0, "y": 4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": -3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7, "x": 0, "y": -1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 0.8, "y": 0.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 0.9, "y": 0.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "x": 1.05, "y": 1.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "CCung": {"translate": [{"time": 0.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 0, "y": -5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 0, "y": 4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 0, "y": -3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": 0, "y": 2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "x": 0, "y": -1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0.8, "y": 0.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 0.9, "y": 0.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1.05, "y": 1.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "YHy": {"translate": [{"time": 0.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 0, "y": -5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 0, "y": 4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": -3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7, "x": 0, "y": -1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 0.8, "y": 0.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 0.9, "y": 0.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "x": 1.05, "y": 1.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9667, "x": 1, "y": 1}]}, "N1": {"translate": [{"time": 0.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 0, "y": -5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 0, "y": 4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": -3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7, "x": 0, "y": -1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9667, "x": 0, "y": 0}], "scale": [{"time": 0.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 0.8, "y": 0.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 0.9, "y": 0.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "x": 1.05, "y": 1.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9667, "x": 1, "y": 1}]}, "PPhat": {"translate": [{"time": 0.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 0, "y": -5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 0, "y": 4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 0, "y": -3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": 0, "y": 2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "x": 0, "y": -1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0.8, "y": 0.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 0.9, "y": 0.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1.05, "y": 1.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "HPhat": {"translate": [{"time": 0.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 0, "y": -5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 0, "y": 4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": -3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7, "x": 0, "y": -1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 0.8, "y": 0.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 0.9, "y": 0.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "x": 1.05, "y": 1.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "APhat": {"translate": [{"time": 0.1, "x": 0, "y": 0.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 0, "y": -5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 0, "y": 4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": -3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7, "x": 0, "y": -1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "x": 0.8, "y": 0.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "x": 0.9, "y": 0.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "x": 1.05, "y": 1.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9667, "x": 1, "y": 1}]}, "TTai": {"translate": [{"time": 0.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 0, "y": -5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 0, "y": 4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": -3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7, "x": 0, "y": -1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "x": 0.8, "y": 0.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "x": 0.9, "y": 0.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "x": 1.05, "y": 1.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9667, "x": 1, "y": 1}]}, "ATai": {"translate": [{"time": 0.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 0, "y": -5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 0, "y": 4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": -3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7, "x": 0, "y": -1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9667, "x": 0, "y": 0}], "scale": [{"time": 0.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 0.8, "y": 0.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 0.9, "y": 0.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "x": 1.05, "y": 1.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9667, "x": 1, "y": 1}]}, "ITai": {"translate": [{"time": 0.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 0, "y": -5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 0, "y": 4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 0, "y": -3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": 0, "y": 2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "x": 0, "y": -1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0.8, "y": 0.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 0.9, "y": 0.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1.05, "y": 1.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "DauHoi": {"translate": [{"time": 0.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 0, "y": -5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 0, "y": 4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 0, "y": -3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": 0, "y": 2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "x": 0, "y": -1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0.8, "y": 0.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 0.9, "y": 0.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1.05, "y": 1.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "DauHuyen": {"translate": [{"time": 0.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 0, "y": -5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 0, "y": 4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 0, "y": -3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": 0, "y": 2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "x": 0, "y": -1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0.8, "y": 0.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 0.9, "y": 0.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1.05, "y": 1.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "TPhat": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 0, "y": -5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 0, "y": 4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "x": 0, "y": -3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 0, "y": 2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "x": 0, "y": -1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0.8, "y": 0.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "x": 0.9, "y": 0.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1.05, "y": 1.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9, "x": 1, "y": 1}]}, "DauSac": {"translate": [{"time": 0.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 0, "y": -5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 0, "y": -3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 0, "y": 2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 0, "y": -1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0.0333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 0.8, "y": 0.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 0.9, "y": 0.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "x": 1.05, "y": 1.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9333, "x": 1, "y": 1}]}, "Sparkle5": {"scale": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 0, "y": 0}]}, "Sparkle4": {"scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 0}]}, "Sparkle6": {"scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 0}]}, "Sparkle3": {"scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 0, "y": 0}]}, "Sparkle2": {"scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 0, "y": 0}]}, "Sparkle7": {"scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "x": 0, "y": 0}]}, "Sparkle": {"scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "x": 0, "y": 0}]}, "Sparkle8": {"scale": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 0, "y": 0}]}, "Sparkle9": {"scale": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 0, "y": 0}]}, "Sparkle10": {"scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 0}]}, "Sparkle11": {"scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 0}]}}}, "Wild_Idle": {"slots": {"APhat": {"attachment": [{"time": 0, "name": null}]}, "APhatLight": {"attachment": [{"time": 0, "name": null}]}, "APhatShadow": {"attachment": [{"time": 0, "name": null}]}, "ATai": {"attachment": [{"time": 0, "name": null}]}, "ATaiLight": {"attachment": [{"time": 0, "name": null}]}, "ATaiShadow": {"attachment": [{"time": 0, "name": null}]}, "CCung": {"attachment": [{"time": 0, "name": null}]}, "CCungLight": {"attachment": [{"time": 0, "name": null}]}, "CCungShadow": {"attachment": [{"time": 0, "name": null}]}, "DauHoi": {"attachment": [{"time": 0, "name": null}]}, "DauHoiLight": {"attachment": [{"time": 0, "name": null}]}, "DauHuyen": {"attachment": [{"time": 0, "name": null}]}, "DauHuyenLight": {"attachment": [{"time": 0, "name": null}]}, "DauSac": {"attachment": [{"time": 0, "name": null}]}, "DauSacLight": {"attachment": [{"time": 0, "name": null}]}, "DauSacShadow": {"attachment": [{"time": 0, "name": null}]}, "GCung": {"attachment": [{"time": 0, "name": null}]}, "GCungLight": {"attachment": [{"time": 0, "name": null}]}, "GCungShadow": {"attachment": [{"time": 0, "name": null}]}, "GongLight": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "color": "ffffff00"}]}, "HHy": {"attachment": [{"time": 0, "name": null}]}, "HHyLight": {"attachment": [{"time": 0, "name": null}]}, "HHyShadow": {"attachment": [{"time": 0, "name": null}]}, "HPhat": {"attachment": [{"time": 0, "name": null}]}, "HPhatLight": {"attachment": [{"time": 0, "name": null}]}, "HPhatShadow": {"attachment": [{"time": 0, "name": null}]}, "ITai": {"attachment": [{"time": 0, "name": null}]}, "ITaiLight": {"attachment": [{"time": 0, "name": null}]}, "ITaiShadow": {"attachment": [{"time": 0, "name": null}]}, "NCung": {"attachment": [{"time": 0, "name": null}]}, "NCungLight": {"attachment": [{"time": 0, "name": null}]}, "NCungShadow": {"attachment": [{"time": 0, "name": null}]}, "PPhat": {"attachment": [{"time": 0, "name": null}]}, "PPhatLight": {"attachment": [{"time": 0, "name": null}]}, "PPhatShadow": {"attachment": [{"time": 0, "name": null}]}, "Sparkle": {"attachment": [{"time": 0, "name": null}]}, "Sparkle2": {"attachment": [{"time": 0, "name": null}]}, "Sparkle3": {"attachment": [{"time": 0, "name": null}]}, "Sparkle4": {"attachment": [{"time": 0, "name": null}]}, "Sparkle5": {"attachment": [{"time": 0, "name": null}]}, "Sparkle6": {"attachment": [{"time": 0, "name": null}]}, "Sparkle7": {"attachment": [{"time": 0, "name": null}]}, "Sparkle8": {"attachment": [{"time": 0, "name": null}]}, "Sparkle9": {"attachment": [{"time": 0, "name": null}]}, "Sparkle10": {"attachment": [{"time": 0, "name": null}]}, "Sparkle11": {"attachment": [{"time": 0, "name": null}]}, "TPhat": {"attachment": [{"time": 0, "name": null}]}, "TPhatLight": {"attachment": [{"time": 0, "name": null}]}, "TPhatShadow": {"attachment": [{"time": 0, "name": null}]}, "TTai": {"attachment": [{"time": 0, "name": null}]}, "TTaiLight": {"attachment": [{"time": 0, "name": null}]}, "TTaiShadow": {"attachment": [{"time": 0, "name": null}]}, "UCung": {"attachment": [{"time": 0, "name": null}]}, "UCungLight": {"attachment": [{"time": 0, "name": null}]}, "UCungShadow": {"attachment": [{"time": 0, "name": null}]}, "YHy": {"attachment": [{"time": 0, "name": null}]}, "YHyLight": {"attachment": [{"time": 0, "name": null}]}, "YHyShadow": {"attachment": [{"time": 0, "name": null}]}}, "bones": {"Wild": {"scale": [{"time": 0, "x": 1.2, "y": 1.2}]}, "GongLight": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -96.69, "curve": "stepped"}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -96.69}]}}}, "Wild_Win": {"slots": {"APhatLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00"}]}, "ATaiLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00"}]}, "CCungLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00"}]}, "DauSacLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00"}]}, "GCungLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00"}]}, "GongLight": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "color": "ffffff00"}]}, "HHyLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00"}]}, "HPhatLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00"}]}, "ITaiLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00"}]}, "NCungLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00"}]}, "PPhatLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00"}]}, "Sparkle": {"attachment": [{"time": 0, "name": null}]}, "Sparkle2": {"attachment": [{"time": 0, "name": null}]}, "Sparkle3": {"attachment": [{"time": 0, "name": null}]}, "Sparkle4": {"attachment": [{"time": 0, "name": null}]}, "Sparkle5": {"attachment": [{"time": 0, "name": null}]}, "Sparkle6": {"attachment": [{"time": 0, "name": null}]}, "Sparkle7": {"attachment": [{"time": 0, "name": null}]}, "Sparkle8": {"attachment": [{"time": 0, "name": null}]}, "Sparkle9": {"attachment": [{"time": 0, "name": null}]}, "Sparkle10": {"attachment": [{"time": 0, "name": null}]}, "Sparkle11": {"attachment": [{"time": 0, "name": null}]}, "TPhatLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00"}]}, "TTaiLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00"}]}, "UCungLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00"}]}, "Wild": {"attachment": [{"time": 0, "name": null}, {"time": 0.6667, "name": "Wild"}]}, "YHyLight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00"}]}}, "bones": {"Wild": {"rotate": [{"time": 0.6667, "angle": 30, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 0, "y": 4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "x": 0, "y": -3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 0, "y": 2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 0, "y": -1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0, "y": 0}], "scale": [{"time": 0.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 1.401, "y": 1.401, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 1.2, "y": 1.2, "curve": "stepped"}, {"time": 2, "x": 1.2, "y": 1.2}]}, "TextGong": {"rotate": [{"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 25}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.608, 1]}, {"time": 0.1667, "x": 0.801, "y": 0.839, "curve": [0.25, 0, 0.608, 1]}, {"time": 0.3333, "x": 1.377, "y": 1.493, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 0}]}, "GongLight": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -96.69, "curve": "stepped"}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -96.69}]}}}}}